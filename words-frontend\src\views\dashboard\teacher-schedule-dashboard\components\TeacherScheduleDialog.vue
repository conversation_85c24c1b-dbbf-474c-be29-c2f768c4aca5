<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="teacher-schedule-content">
      <!-- 教师信息 -->
      <div class="teacher-info" v-if="teacher">
        <el-avatar :size="40" :src="teacher.avatar">
          {{ teacher.teacherName?.charAt(0) }}
        </el-avatar>
        <div class="teacher-details">
          <div class="teacher-name">{{ teacher.teacherName }}</div>
          <div class="teacher-meta">{{ teacher.phone }} | {{ teacher.groupName }}</div>
        </div>
        <div class="schedule-summary">
          <el-tag type="success" size="large">
            可排课课次: {{ teacher.availableSlots || 0 }}
          </el-tag>
        </div>
      </div>

      <!-- 时间段展示 -->
      <div class="schedule-display" v-loading="loading">
        <div class="schedule-header">
          <h4>{{ formatDate(date) }} 排课情况</h4>
          <div class="legend">
            <span class="legend-item available">
              <span class="legend-color"></span>
              可用时间段
            </span>
            <span class="legend-item occupied">
              <span class="legend-color"></span>
              已排课程
            </span>
            <span class="legend-item unavailable">
              <span class="legend-color"></span>
              不可用
            </span>
          </div>
        </div>

        <!-- 时间轴 -->
        <div class="time-axis">
          <div class="time-slots">
            <div
              v-for="slot in timeSlots"
              :key="`${slot.startTime}-${slot.endTime}`"
              class="time-slot"
              :class="slot.status"
              @click="handleSlotClick(slot)"
            >
              <div class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</div>
              <div class="slot-content">
                <div v-if="slot.status === 'occupied' && slot.course" class="course-info">
                  <div class="course-name">{{ slot.course.courseName }}</div>
                  <div class="student-name">{{ slot.course.studentName }}</div>
                </div>
                <div v-else-if="slot.status === 'available'" class="available-info">
                  <el-icon><Plus /></el-icon>
                  <span>可排课</span>
                </div>
                <div v-else class="unavailable-info">
                  <span>不可用</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty 
          v-if="!loading && timeSlots.length === 0" 
          description="该教师当天无可用时间段"
          :image-size="100"
        />
      </div>

      <!-- 统计信息 -->
      <div class="schedule-stats" v-if="scheduleStats">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ scheduleStats.totalSlots }}</div>
              <div class="stat-label">总时间段</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ scheduleStats.availableSlots }}</div>
              <div class="stat-label">可用时间段</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ scheduleStats.occupiedSlots }}</div>
              <div class="stat-label">已排课程</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ scheduleStats.utilizationRate }}%</div>
              <div class="stat-label">利用率</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="viewFullSchedule">
          <el-icon><Calendar /></el-icon>
          查看完整排课
        </el-button>
        <el-button @click="exportSchedule" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出排课
        </el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Calendar, Download } from '@element-plus/icons-vue'
import { teacherScheduleDashboardApi } from '@/api/management/teacher-schedule-dashboard'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  },
  date: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const timeSlots = ref([])
const scheduleStats = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  if (!props.teacher || !props.date) return '教师排课详情'
  return `${props.teacher.teacherName} - ${formatDate(props.date)} 排课详情`
})

// 方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const loadScheduleDetail = async () => {
  if (!props.teacher || !props.date) return

  loading.value = true
  try {
    const response = await teacherScheduleDashboardApi.getTeacherScheduleDetail(
      props.teacher.teacherId, 
      props.date
    )
    
    const data = response.data || {}
    timeSlots.value = data.timeSlots || []
    scheduleStats.value = data.stats || null

    // 如果没有后端数据，生成模拟数据
    if (timeSlots.value.length === 0) {
      generateMockTimeSlots()
    }
  } catch (error) {
    ElMessage.error('加载排课详情失败: ' + error.message)
    // 生成模拟数据作为降级
    generateMockTimeSlots()
  } finally {
    loading.value = false
  }
}

const generateMockTimeSlots = () => {
  // 生成模拟的时间段数据
  const mockSlots = [
    {
      startTime: '09:00',
      endTime: '10:00',
      status: 'available'
    },
    {
      startTime: '10:00',
      endTime: '11:00',
      status: 'occupied',
      course: {
        courseName: '英语口语课',
        studentName: '张小明'
      }
    },
    {
      startTime: '11:00',
      endTime: '12:00',
      status: 'available'
    },
    {
      startTime: '14:00',
      endTime: '15:00',
      status: 'available'
    },
    {
      startTime: '15:00',
      endTime: '16:00',
      status: 'occupied',
      course: {
        courseName: '数学辅导',
        studentName: '李小红'
      }
    },
    {
      startTime: '16:00',
      endTime: '17:00',
      status: 'unavailable'
    }
  ]

  timeSlots.value = mockSlots

  // 计算统计信息
  const totalSlots = mockSlots.length
  const availableSlots = mockSlots.filter(slot => slot.status === 'available').length
  const occupiedSlots = mockSlots.filter(slot => slot.status === 'occupied').length
  const utilizationRate = totalSlots > 0 ? Math.round((occupiedSlots / totalSlots) * 100) : 0

  scheduleStats.value = {
    totalSlots,
    availableSlots,
    occupiedSlots,
    utilizationRate
  }
}

const handleSlotClick = (slot) => {
  if (slot.status === 'available') {
    ElMessage.info('可以在此时间段安排课程')
  } else if (slot.status === 'occupied') {
    ElMessage.info(`已安排课程: ${slot.course?.courseName}`)
  } else {
    ElMessage.info('此时间段不可用')
  }
}

const viewFullSchedule = () => {
  // 这里可以跳转到教师完整排课页面
  ElMessage.info('查看完整排课功能开发中...')
}

const exportSchedule = async () => {
  exporting.value = true
  try {
    // 这里实现排课数据导出功能
    ElMessage.info('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    exporting.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadScheduleDetail()
  }
})

watch(() => [props.teacher, props.date], () => {
  if (props.modelValue) {
    loadScheduleDetail()
  }
})
</script>

<style lang="scss" scoped>
.teacher-schedule-content {
  .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .teacher-details {
      flex: 1;

      .teacher-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .teacher-meta {
        font-size: 12px;
        color: #909399;
      }
    }

    .schedule-summary {
      flex-shrink: 0;
    }
  }

  .schedule-display {
    margin-bottom: 20px;

    .schedule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: #303133;
      }

      .legend {
        display: flex;
        gap: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #606266;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
          }

          &.available .legend-color {
            background-color: #67c23a;
          }

          &.occupied .legend-color {
            background-color: #409eff;
          }

          &.unavailable .legend-color {
            background-color: #f56c6c;
          }
        }
      }
    }

    .time-slots {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;

      .time-slot {
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.available {
          border-color: #67c23a;
          background-color: #f0f9ff;

          &:hover {
            background-color: #e1f3d8;
          }
        }

        &.occupied {
          border-color: #409eff;
          background-color: #ecf5ff;

          &:hover {
            background-color: #d9ecff;
          }
        }

        &.unavailable {
          border-color: #f56c6c;
          background-color: #fef0f0;
          cursor: not-allowed;

          &:hover {
            transform: none;
            background-color: #fde2e2;
          }
        }

        .slot-time {
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
          text-align: center;
        }

        .slot-content {
          text-align: center;

          .course-info {
            .course-name {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }

            .student-name {
              font-size: 12px;
              color: #909399;
            }
          }

          .available-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #67c23a;
            font-size: 14px;
          }

          .unavailable-info {
            color: #f56c6c;
            font-size: 14px;
          }
        }
      }
    }
  }

  .schedule-stats {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
