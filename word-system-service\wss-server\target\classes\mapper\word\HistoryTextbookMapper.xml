<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.HistoryTextbookMapper">
    
    <resultMap type="HistoryTextbook" id="HistoryTextbookResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="textbookObj"    column="textbook_obj"    />
        <result property="textbookItemsObj"    column="textbook_items_obj"    />
        <result property="changeDescription"    column="change_description"    />
        <result property="deleted"    column="deleted"    />
        <result property="version"    column="version"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectHistoryTextbookVo">
        select id, textbook_id, textbook_obj, textbook_items_obj, change_description, deleted, version, updated_by, updated_at from history_textbook
    </sql>

    <select id="selectHistoryTextbookList" parameterType="HistoryTextbook" resultMap="HistoryTextbookResult">
        <include refid="selectHistoryTextbookVo"/>
        <where>  
            <if test="textbookId != null  and textbookId != ''"> and textbook_id = #{textbookId}</if>
            <if test="textbookObj != null  and textbookObj != ''"> and textbook_obj = #{textbookObj}</if>
            <if test="textbookItemsObj != null  and textbookItemsObj != ''"> and textbook_items_obj = #{textbookItemsObj}</if>
            <if test="changeDescription != null  and changeDescription != ''"> and change_description = #{changeDescription}</if>
            <if test="deleted != null  and deleted != ''"> and deleted = #{deleted}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updatedAt != null  and updatedAt != ''"> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectHistoryTextbookById" parameterType="String" resultMap="HistoryTextbookResult">
    </select>

    <insert id="insertHistoryTextbook" parameterType="HistoryTextbook">
        insert into history_textbook
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null">textbook_id,</if>
            <if test="textbookObj != null and textbookObj != ''">textbook_obj,</if>
            <if test="textbookItemsObj != null and textbookItemsObj != ''">textbook_items_obj,</if>
            <if test="changeDescription != null">change_description,</if>
            <if test="deleted != null">deleted,</if>
            <if test="version != null">version,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null">#{textbookId},</if>
            <if test="textbookObj != null and textbookObj != ''">#{textbookObj},</if>
            <if test="textbookItemsObj != null and textbookItemsObj != ''">#{textbookItemsObj},</if>
            <if test="changeDescription != null">#{changeDescription},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="version != null">#{version},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateHistoryTextbook" parameterType="HistoryTextbook">
        update history_textbook
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="textbookObj != null and textbookObj != ''">textbook_obj = #{textbookObj},</if>
            <if test="textbookItemsObj != null and textbookItemsObj != ''">textbook_items_obj = #{textbookItemsObj},</if>
            <if test="changeDescription != null">change_description = #{changeDescription},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="version != null">version = #{version},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHistoryTextbookById" parameterType="String">
        delete from history_textbook where id = #{id}
    </delete>

    <delete id="deleteHistoryTextbookByIds" parameterType="String">
        delete from history_textbook where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>