<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TeacherProfileMapper">

    <resultMap id="TeacherDetailRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$DetailResp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="avatar" property="avatar"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="current_location" property="currentLocation"/>
        <result column="employment_type" property="employmentType"/>
        <result column="current_status" property="currentStatus"/>

        <!-- 教育背景 -->
        <result column="education" property="education"/>
        <result column="graduate_school" property="graduateSchool"/>
        <result column="major" property="major"/>
        <result column="university_type" property="universityType"/>
        <result column="is_normal_university" property="isNormalUniversity"/>
        <result column="study_abroad" property="studyAbroad"/>
        <result column="study_abroad_country" property="studyAbroadCountry"/>

        <!-- 教学资质 -->
        <result column="teaching_certificate_level" property="teachingCertificateLevel"/>
        <result column="subjects" property="subjects" typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="english_qualification" property="englishQualification"/>
        <result column="mandarin_qualification" property="mandarinQualification"/>
        <result column="communication_ability" property="communicationAbility"/>

        <!-- 教学经历 -->
        <result column="teaching_experience" property="teachingExperience"/>
        <result column="taught_courses" property="taughtCourses" typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="teaching_years" property="teachingYears"/>
        <result column="awards" property="awards"/>

        <!-- 教学风格和适配 -->
        <result column="teaching_style" property="teachingStyle" typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="english_pronunciation" property="englishPronunciation"/>
        <result column="suitable_grades" property="suitableGrades" typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="suitable_levels" property="suitableLevels" typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="suitable_personality" property="suitablePersonality"/>

        <result column="other" property="other"/>
        <result column="introduction" property="introduction"/>
        <result column="summer_schedule_type" property="summerScheduleType"/>

        <!-- 新增字段 -->
        <result column="formal_entry_date" property="formalEntryDate"/>
        <result column="qualification_certificates" property="qualificationCertificates"/>
        <result column="demo_videos" property="demoVideos"/>

        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>

        <!-- 新增统计字段 -->
        <result column="trial_course_pass_rate" property="trialCoursePassRate"/>
        <result column="total_teaching_hours" property="totalTeachingHours"/>
        <result column="current_student_count" property="currentStudentCount"/>
    </resultMap>

    <resultMap id="AvailableRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$AvailableResp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="BasicRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$BasicResp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="avatar" property="avatar"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="UserRoleRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$UserRoleResp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="avatar" property="avatar"/>
    </resultMap>

    <resultMap id="TeachingInfoRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$TeachingInfoResp">
        <result column="teacher_id" property="teacherId"/>
        <result column="teacher_name" property="teacherName"/>
        <result column="current_students" property="currentStudents"/>
        <result column="unconsumed_hours" property="unconsumedHours"/>
        <result column="consumed_hours" property="consumedHours"/>
        <result column="total_hours" property="totalHours"/>
    </resultMap>

    <!-- 查询教师详细信息 -->
    <select id="selectTeacherDetail" resultMap="TeacherDetailRespMap">
        SELECT
            tp.teacher_id as id,
            tp.real_name as name,
            tp.nick_name as nickname,
            tp.gender,
            tp.age,
            tp.phonenumber as phone,
            '' as email,
            tp.avatar,
            tgm.group_id,
            tg.name as group_name,
            tp.current_location,
            tp.employment_type,
            tp.current_status,

            -- 教育背景
            tp.education,
            tp.graduate_school,
            tp.major,
            tp.university_type,
            tp.is_normal_university,
            tp.study_abroad,
            tp.study_abroad_country,

            -- 教学资质
            tp.teaching_certificate_level,
            tp.subjects,
            tp.training_subjects,
            tp.english_qualification,
            tp.mandarin_qualification,
            tp.communication_ability,

            -- 教学经历
            tp.teaching_experience,
            tp.taught_courses,
            tp.teaching_years,
            tp.awards,

            -- 教学风格和适配
            tp.teaching_style,
            tp.english_pronunciation,
            tp.suitable_grades,
            tp.suitable_levels,
            tp.suitable_personality,

            tp.other,
            tp.introduction,
            tp.summer_schedule_type,

            -- 新增字段
            tp.formal_entry_date,
            tp.qualification_certificates,
            tp.demo_videos,

            tp.status,
            tp.create_time,
            tp.update_time
        FROM teacher_profile tp
        LEFT JOIN teaching_group_member tgm ON tp.teacher_id = tgm.teacher_id AND tgm.deleted = false AND tgm.status = 'active'
        LEFT JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        WHERE tp.teacher_id = #{teacherId} AND tp.deleted = false AND tp.status != 'deleted'
    </select>

    <!-- 查询可分配的教师列表 -->
    <select id="selectAvailableTeachers" resultMap="AvailableRespMap">
        SELECT
            tp.teacher_id as id,
            tp.nick_name as name,
            tp.phonenumber as phone,
            '' as email,
            tp.status
        FROM teacher_profile tp
        WHERE tp.deleted = false
        AND tp.status != 'deleted'
        AND tp.teacher_id NOT IN (
            SELECT DISTINCT tgm.teacher_id
            FROM teaching_group_member tgm
            INNER JOIN teaching_group tg ON tgm.group_id = tg.id
            WHERE tgm.deleted = false AND tg.deleted = false AND tgm.status = 'active'
        )
        ORDER BY tp.create_time DESC
    </select>



    <!-- 查询所有教师列表 -->
    <select id="selectAllTeachers" resultMap="UserRoleRespMap">
        SELECT
            tp.teacher_id as id,
            tp.real_name as name,
            tp.phonenumber as phone,
            '' as email,
            tp.avatar
        FROM teacher_profile tp
        WHERE tp.deleted = false
        ORDER BY tp.create_time DESC
    </select>

    <!-- 查询教师带教信息 -->
    <select id="selectTeachingInfo" resultMap="TeachingInfoRespMap">
        SELECT
            #{teacherId} as teacher_id,
            COALESCE(tp.nick_name, tp.real_name) as teacher_name,
            COALESCE(student_stats.current_students, 0) as current_students,
            COALESCE(course_stats.unconsumed_hours, 0) as unconsumed_hours,
            COALESCE(course_stats.consumed_hours, 0) as consumed_hours,
            COALESCE(course_stats.total_hours, 0) as total_hours
        FROM teacher_profile tp
        LEFT JOIN (
            SELECT
                teacher_id,
                COUNT(DISTINCT student_id) as current_students
            FROM course
            WHERE teacher_id = #{teacherId}
            AND course_status IN ('待开始', '进行中')
            AND deleted = false
            GROUP BY teacher_id
        ) student_stats ON tp.teacher_id::text = student_stats.teacher_id
        LEFT JOIN (
            SELECT
                teacher_id,
                SUM(CASE WHEN course_status = '待开始' THEN 1 ELSE 0 END) as unconsumed_hours,
                SUM(CASE WHEN course_status = '已完成' THEN 1 ELSE 0 END) as consumed_hours,
                COUNT(*) as total_hours
            FROM course
            WHERE teacher_id = #{teacherId} AND deleted = false
            GROUP BY teacher_id
        ) course_stats ON tp.teacher_id::text = course_stats.teacher_id
        WHERE tp.teacher_id = #{teacherId} AND tp.deleted = false
    </select>

    <!-- 批量查询教师带教信息 -->
    <select id="selectTeachingInfoBatch" resultMap="TeachingInfoRespMap">
        SELECT
            tp.teacher_id as teacher_id,
            COALESCE(tp.nick_name, tp.real_name) as teacher_name,
            COALESCE(student_stats.current_students, 0) as current_students,
            COALESCE(course_stats.unconsumed_hours, 0) as unconsumed_hours,
            COALESCE(course_stats.consumed_hours, 0) as consumed_hours,
            COALESCE(course_stats.total_hours, 0) as total_hours
        FROM teacher_profile tp
        LEFT JOIN (
            SELECT
                teacher_id,
                COUNT(DISTINCT student_id) as current_students
            FROM course
            WHERE teacher_id IN
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
            AND course_status IN ('待开始', '进行中')
            AND deleted = false
            GROUP BY teacher_id
        ) student_stats ON tp.teacher_id::text = student_stats.teacher_id
        LEFT JOIN (
            SELECT
                teacher_id,
                SUM(CASE WHEN course_status = '待开始' THEN 1 ELSE 0 END) as unconsumed_hours,
                SUM(CASE WHEN course_status = '已完成' THEN 1 ELSE 0 END) as consumed_hours,
                COUNT(*) as total_hours
            FROM course
            WHERE teacher_id IN
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
            AND deleted = false
            GROUP BY teacher_id
        ) course_stats ON tp.teacher_id::text = course_stats.teacher_id
        WHERE tp.teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}::int8
        </foreach>
        AND tp.deleted = false
        ORDER BY tp.create_time DESC
    </select>

    <!-- 批量查询教师详细信息 -->
    <select id="selectTeacherDetailBatch" resultMap="TeacherDetailRespMap">
        SELECT
            tp.teacher_id as id,
            tp.real_name as name,
            tp.nick_name as nickname,
            tp.gender,
            tp.age,
            tp.phonenumber as phone,
            '' as email,
            tp.avatar,
            tgm.group_id,
            tg.name as group_name,
            tp.current_location,
            tp.employment_type,
            tp.current_status,

            -- 教育背景
            tp.education,
            tp.graduate_school,
            tp.major,
            tp.university_type,
            tp.is_normal_university,
            tp.study_abroad,
            tp.study_abroad_country,

            -- 教学资质
            tp.teaching_certificate_level,
            tp.subjects,
            tp.english_qualification,
            tp.mandarin_qualification,
            tp.communication_ability,

            -- 教学经历
            tp.teaching_experience,
            tp.taught_courses,
            tp.teaching_years,
            tp.awards,

            -- 教学风格和适配
            tp.teaching_style,
            tp.english_pronunciation,
            tp.suitable_grades,
            tp.suitable_levels,
            tp.suitable_personality,

            tp.other,
            tp.introduction,
            tp.summer_schedule_type,

            -- 新增字段
            tp.formal_entry_date,
            tp.qualification_certificates,
            tp.demo_videos,

            tp.status,
            tp.create_time,
            tp.update_time
        FROM teacher_profile tp
        LEFT JOIN teaching_group_member tgm ON tp.teacher_id = tgm.teacher_id AND tgm.deleted = false AND tgm.status = 'active'
        LEFT JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        WHERE tp.teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}::int8
        </foreach>
        AND tp.deleted = false
        ORDER BY tp.create_time DESC
    </select>

</mapper>
