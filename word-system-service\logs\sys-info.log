21:29:59.824 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
21:29:59.875 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 27252 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
21:29:59.876 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
21:30:02.258 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
21:30:03.183 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
21:30:03.184 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
21:30:03.184 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
21:30:03.257 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
21:30:03.853 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
21:30:05.211 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
21:30:05.270 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
21:30:05.383 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
21:30:05.454 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
21:30:06.508 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
21:30:06.684 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
21:30:06.738 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
21:30:13.455 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
21:30:13.458 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
21:30:15.356 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
21:30:15.376 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:30:15.377 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
21:30:15.378 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
21:30:15.379 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:30:15.379 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:30:15.379 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
21:30:15.379 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3cf6f3aa
21:30:17.124 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
21:30:17.134 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:30:17.144 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 17.706 seconds (process running for 19.873)
21:31:24.177 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:31:27.991 [schedule-pool-1] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[15005014480][Success][登录成功]
21:31:40.065 [schedule-pool-1] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[15005014480][Logout][退出成功]
21:31:46.711 [schedule-pool-2] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:40:16.323 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/************:19736
21:40:16.513 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
21:56:29.216 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/************:19736
21:56:29.405 [lettuce-nioEventLoop-4-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
21:56:29.550 [schedule-pool-2] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[admin][Logout][退出成功]
21:56:31.748 [schedule-pool-4] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[admin][Success][登录成功]
22:02:55.365 [http-nio-8080-exec-21] INFO  o.n.w.t.a.u.AllinPaySignUtil - [sign,40] - [1|超级管理员] - [统一支付] - 签名前参数：appid=00000003&body=测试商品标题&cusid=990440148166000&expiretime=20250805220755&front_url=https://your-domain.com/pay/success&notify_url=https://your-domain.com/allinpay/notify&randomstr=20250805220229158011513648896658900&remark=订单描述备注&reqsn=20250805220229158011513648896658900&signtype=RSA&trxamt=1&version=12
22:02:55.382 [http-nio-8080-exec-21] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,181] - [1|超级管理员] - [统一主扫支付] - 支付参数：{trxamt=1, reqsn=20250805220229158011513648896658900, body=测试商品标题, remark=订单描述备注, expiretime=20250805220755, notify_url=https://your-domain.com/allinpay/notify, front_url=https://your-domain.com/pay/success, signtype=RSA, randomstr=20250805220229158011513648896658900, version=12, cusid=990440148166000, appid=00000003, sign=Pvbpn29cQ9ZlGGtVIiKAfSu+G3VDeybsSLp/6LcB3Wa4MWG8khazCFSFQuZc5aWkVZoeq0//nK0gXX8YQaJ6p+TlbYInWnp22gfteHkw7ABCw+kpOYMdsLZEuTWStJ8HN9iS09FyKZkQfHPcckiv5Ve65S0hHQ7Ch+UQMDj2XMRWWUCRkFMcPDWTpcvbBZWIMidDYzpUZX2vC/1wsmDpMilKeZ9RHx+VAj+TIT72tFhmZxQqcGxBTXGvdktzzg52giBweLNJ6PUNxMjQXmEoztxYjLqzMLCpYKN3NChpgNu3zgNlh85eLyyJn+MZWKpfjg4n7dK9fIEKLg3Zepcn+A==}
22:02:55.759 [http-nio-8080-exec-21] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,190] - [1|超级管理员] - [统一主扫支付] - 接口返回：{"appid":"00000003","cusid":"990440148166000","errmsg":"生成收款码成功","payinfo":"https://syb.allinpay.com/apiweb/h5unionpay/unionnative?token=jVFUuUov90oI6t8JiGWpCsNZo8qyhulvzTUVyCZuG0v8NGiyc7uyMSq8y4AhLhv1fTixy9vZnhVs6d5nG5uWG5LjxMg1xis4fBtiRNasPsqba4rWTyT43M4DOZd5EtcgfFvASkZXlgzA9PbWIr544yBofn","randomstr":"816183873744","reqsn":"20250805220229158011513648896658900","retcode":"SUCCESS","sign":"Y1Us1kLUW9VKweOgWxkbbzbxpIM41d1SLZodBTyydHnE0b7n2I8Gco2N5+eeY6R8mxG2jrIUu05GnrduN98AuB67xdLDfkepY9Askkijm5oyqllD3yHmB/Obk9psZw+od+MDLPVpdaGoar+c++x7HQDvVcpee1PtzOcS7j2dYZw=","trxstatus":"0000"}
22:05:50.575 [http-nio-8080-exec-27] INFO  o.n.w.t.a.u.AllinPaySignUtil - [sign,40] - [1|超级管理员] - [统一支付] - 签名前参数：appid=00000003&body=测试商品标题&cusid=990440148166000&expiretime=20250805221050&front_url=https://your-domain.com/pay/success&notify_url=https://your-domain.com/allinpay/notify&randomstr=20250805220229158011513648896658900&remark=订单描述备注&reqsn=20250805220229158011513648896658900&signtype=RSA&trxamt=1&version=12
22:05:50.585 [http-nio-8080-exec-27] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,181] - [1|超级管理员] - [统一主扫支付] - 支付参数：{trxamt=1, reqsn=20250805220229158011513648896658900, body=测试商品标题, remark=订单描述备注, expiretime=20250805221050, notify_url=https://your-domain.com/allinpay/notify, front_url=https://your-domain.com/pay/success, signtype=RSA, randomstr=20250805220229158011513648896658900, version=12, cusid=990440148166000, appid=00000003, sign=yFBkabdChnKK5ZPN8qG8m/fmZx8WJCnICYJpXmCdsAgVW0Bj7CgsaWb2yIMd+ls5VIyedmpUA3+7DisubEflX71vy9wm8hshmLIG4eDkLBjIYaf7zMvP+PjVVvQRyNBBqu5VczkQX9D9KpMsG4EfmOiQW2fUYDmxSRTn0Sp+FOZ6eJ3oOVZYZQyfaJc6HkTqeX133kUBMy3DjHr9YgNqYJqVZhFrUATVop+vp8zhUstWP8MIWF/Bf5cN9Slqo1Uk2TXtWJxoNfWZf9SMf8GROFCDkSQS32HeJ6qFnwopBJZ2Z/3ncu6LByJ9exXhv1Y4xlQGrt9YJTZ4FLlxOSRYyQ==}
22:05:50.720 [http-nio-8080-exec-27] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,190] - [1|超级管理员] - [统一主扫支付] - 接口返回：{"appid":"00000003","cusid":"990440148166000","errmsg":"生成收款码成功","payinfo":"https://syb.allinpay.com/apiweb/h5unionpay/unionnative?token=jVFUuUov90oI6t8JiGWpCsNZo8qyhulvzTUVyCZuG0v8NGiyc7uyMSq8y4AhLhv1fTixy9vZnhVs6d5nG5uWG5LjxMg1xis4fBtiRNasPsqba4rWTyT43M4DOZd5EtcgfFvASkZXlgzA9PbWIr544yBofn","randomstr":"713248635085","reqsn":"20250805220229158011513648896658900","retcode":"SUCCESS","sign":"kRtdvP062DBP4MEBOWSv53wUbkOVnJGk1Ud2xzrXXyivhwHM6/9VEWmy9w5OZ/JH+epUq0/gSqleuW+xCkC/cxHp6D9GKs1w/Tan6j1qEK/F+5D9ZTm3hYYMABW1K2PKIvjRe7jE16JWEmC144l4d4b1nDTC4i1r9/nvYqZQ+Wk=","trxstatus":"0000"}
22:09:20.736 [http-nio-8080-exec-46] INFO  o.n.w.r.c.TeacherScheduleDashboardController - [getDashboard,34] - [1|超级管理员] - 获取老师可排课时间看板: TeacherScheduleDashboardDto.QueryReq(startDate=2025-08-06, endDate=2025-08-06, groupIds=[])
22:09:20.736 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.TeacherScheduleDashboardServiceImpl - [getDashboard,45] - [1|超级管理员] - 获取老师可排课时间看板: startDate=2025-08-06, endDate=2025-08-06, groupIds=[]
22:09:20.740 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.TeacherScheduleDashboardServiceImpl - [getActiveTeachersInGroups,173] - [1|超级管理员] - 查询已加入教学组且状态为active的教师列表: groupIds=[]
22:09:20.847 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.TeacherScheduleDashboardServiceImpl - [getActiveTeachersInGroups,197] - [1|超级管理员] - 查询到符合条件的教师数量: 113
22:09:20.852 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.TeacherManagementServiceImpl - [getTeacherTimeSlotsMap,108] - [1|超级管理员] - 批量获取教师时间表: teacherIds=[7510, 4732, 7667, 5197, 7373, 5480, 7883, 5031, 7847, 7862, 4926, 7852, 7412, 7410, 6682, 4930, 4074, 5569, 5455, 3791, 5618, 5687, 5673, 7378, 6007, 3786, 7115, 7375, 3954, 6980, 7678, 4444, 6911, 6910, 6681, 6545, 6143, 4575, 7479, 4688, 4689, 6912, 3976, 5395, 3839, 4690, 6187, 4693, 4695, 6440, 6188, 5189, 5191, 5061, 4029, 4697, 7199, 5527, 4704, 6439, 7478, 7284, 5724, 5845, 5136, 6840, 8051, 8175, 7932, 6179, 4056, 7711, 6784, 6782, 5447, 7162, 6437, 4868, 7900, 6909, 6008, 7962, 3940, 6177, 6249, 4467, 6913, 8061, 7503, 7668, 5528, 4901, 7853, 8273, 8340, 8341, 7720, 7716, 4932, 7374, 6674, 7022, 7414, 6970, 5108, 4696, 8342, 8343, 5542, 5762, 5664, 3850, 7315]
22:11:23.919 [http-nio-8080-exec-34] INFO  o.n.w.r.c.CourseQueryController - [list,42] - [1|超级管理员] - 接收到课程查询请求: CourseQueryDto.QueryRequest(subject=null, specification=null, type=null, courseType=null, courseStatuses=[], teacherId=null, teacherName=null, studentId=null, studentName=null, startDate=null, endDate=null, antiForgetCompleted=null, exceptionTypes=[], hasCourseConsumption=null, pageNum=1, pageSize=10, orderBy=scheduledStartTime, orderDirection=desc)
22:11:23.919 [http-nio-8080-exec-43] INFO  o.n.w.r.c.CourseQueryController - [statistics,66] - [1|超级管理员] - 接收到课程统计请求: CourseQueryDto.QueryRequest(subject=null, specification=null, type=null, courseType=null, courseStatuses=[], teacherId=null, teacherName=null, studentId=null, studentName=null, startDate=null, endDate=null, antiForgetCompleted=null, exceptionTypes=[], hasCourseConsumption=null, pageNum=1, pageSize=10, orderBy=scheduledStartTime, orderDirection=desc)
22:11:23.952 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseQueryServiceImpl - [getStatistics,96] - [1|超级管理员] - 开始统计课程信息，查询条件: CourseQueryDto.QueryRequest(subject=null, specification=null, type=null, courseType=null, courseStatuses=[], teacherId=null, teacherName=null, studentId=null, studentName=null, startDate=null, endDate=null, antiForgetCompleted=null, exceptionTypes=[], hasCourseConsumption=null, pageNum=1, pageSize=10, orderBy=scheduledStartTime, orderDirection=desc)
22:11:23.952 [http-nio-8080-exec-34] INFO  o.n.w.s.s.i.CourseQueryServiceImpl - [queryCourses,52] - [1|超级管理员] - 开始查询课程信息，查询条件: CourseQueryDto.QueryRequest(subject=null, specification=null, type=null, courseType=null, courseStatuses=[], teacherId=null, teacherName=null, studentId=null, studentName=null, startDate=null, endDate=null, antiForgetCompleted=null, exceptionTypes=[], hasCourseConsumption=null, pageNum=1, pageSize=10, orderBy=scheduledStartTime, orderDirection=desc)
22:11:23.952 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseQueryServiceImpl - [calculateStatisticsWithAggregation,579] - [1|超级管理员] - 使用优化查询计算统计信息（无JOIN）
22:11:24.498 [http-nio-8080-exec-34] INFO  o.n.w.s.s.i.CourseQueryServiceImpl - [queryCourses,87] - [1|超级管理员] - 课程查询完成，共查询到 50841 条记录
22:11:30.723 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseQueryServiceImpl - [getStatistics,104] - [1|超级管理员] - 课程统计完成: CourseQueryDto.StatisticsResponse(totalStudents=6019, totalCourses=50841, completedCourses=35566, cancelledCourses=4282, rescheduledCourses=2586, antiForgetProgress=3557/15355, totalCourseConsumption=3285.86)
22:15:02.948 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:15:03.105 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
22:15:03.106 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:15:03.107 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
22:15:03.107 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
22:15:03.139 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:15:03.149 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:15:03.168 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:15:56.418 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:15:56.468 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 17352 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
22:15:56.469 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:15:58.642 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:15:59.535 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:15:59.536 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:15:59.536 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:15:59.613 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:16:00.171 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:16:01.539 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:16:01.577 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:16:01.650 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:16:01.690 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:16:02.536 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:16:02.690 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:16:02.723 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:16:08.917 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
22:16:08.921 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
22:16:10.124 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:16:10.137 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:16:10.137 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:16:10.138 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:16:10.138 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:16:10.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:16:10.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:16:10.139 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3829bb5
22:16:11.424 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:16:11.433 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:16:11.446 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 15.421 seconds (process running for 16.566)
22:16:16.580 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:22:59.520 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:22:59.664 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
22:22:59.664 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:22:59.664 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
22:22:59.665 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
22:22:59.680 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:22:59.687 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:22:59.695 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:23:04.668 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:23:04.716 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 24032 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
22:23:04.717 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:23:06.723 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:23:07.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:23:07.534 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:23:07.534 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:23:07.599 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:23:08.089 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:23:09.393 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:23:09.432 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:23:09.505 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:23:09.543 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:23:10.403 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:23:10.529 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:23:10.557 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:23:17.037 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
22:23:17.039 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
22:23:18.293 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:23:18.308 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:23:18.308 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:23:18.309 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:23:18.310 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:23:18.310 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:23:18.310 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:23:18.310 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4be4680b
22:23:19.799 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:23:19.807 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:23:19.820 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 15.551 seconds (process running for 16.632)
22:24:03.755 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:24:09.261 [http-nio-8080-exec-3] INFO  o.n.w.t.a.u.AllinPaySignUtil - [sign,40] - [1|超级管理员] - [统一支付] - 签名前参数：appid=00000003&body=测试商品标题&cusid=990440148166000&expiretime=20250805222909&front_url=https://your-domain.com/pay/success&notify_url=https://your-domain.com/allinpay/notify&randomstr=20250805222404490511514944228564600&remark=订单描述备注&reqsn=20250805222404490511514944228564600&signtype=RSA&trxamt=349900&version=12
22:24:09.275 [http-nio-8080-exec-3] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,181] - [1|超级管理员] - [统一主扫支付] - 支付参数：{trxamt=349900, reqsn=20250805222404490511514944228564600, body=测试商品标题, remark=订单描述备注, expiretime=20250805222909, notify_url=https://your-domain.com/allinpay/notify, front_url=https://your-domain.com/pay/success, signtype=RSA, randomstr=20250805222404490511514944228564600, version=12, cusid=990440148166000, appid=00000003, sign=0zxMa0J01viin1JVsTwllFaWf7UuvqCXhoZ0z4+/oJVlQ/GqaM9807tVcA1eJAXm7tRblNc8izzqo7GXLtGnuj3Oyt5VvCYli9Vn4CI9cX2+/FFvb6SZSJ/vzOnBaWtnzcIM8lRJvMOENci2Ni454USxuXo2aHu+xbl93KmYbjg14u/3im0N8V6fGdhbf1xcGjkwGcYMQKnV5d2+tY4N+jvSVhRKb/DgM6FMRqmo7LTNbcZEAOj+IRNmGuRL39WymVFSWsH9BNRh41wukA9AHDkmThANaN03D830Oitd8uHIEp0GRCbORQNPFyTlYdE72CnQX7kLsMYub89ZC0pSEw==}
22:24:09.626 [http-nio-8080-exec-3] INFO  o.n.w.t.a.s.i.AllinPayServiceImpl - [executeNativepay,190] - [1|超级管理员] - [统一主扫支付] - 接口返回：{"appid":"00000003","cusid":"990440148166000","errmsg":"生成收款码成功","payinfo":"https://syb.allinpay.com/apiweb/h5unionpay/unionnative?token=jVFUuUov90oI6t8JiGWpCSqcf0xVQkhLiWZx2qYID4QNyAqyyhXY35qDchJxe4humnczM3RYq2vwgr3f62XTiHkEUb7ypsqFDjpl2pg5suNjAlKVzqGxChp3M9avuVyNfsjtEgu0YBxPdRP4SGSB1vxpD5","randomstr":"685906564591","reqsn":"20250805222404490511514944228564600","retcode":"SUCCESS","sign":"RlS/mox1hUEOUpp290A/QywvRvrCZuhUiCS0HuWMALqh8UqbxiBd/rEKYPsHfnBYzlwbb9VRTFdZtaD6iOdzKag5wlpLik1ALBpxeFIXnZhuWEva76n2hDr01rxmb6O+no7aHDACTXkGVsIHrdQIzpdrbslFZLxm9fa+OlD0cEw=","trxstatus":"0000"}
22:35:25.108 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/************:19736
22:35:25.319 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
22:41:22.264 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:41:22.488 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
22:41:22.488 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:41:22.489 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
22:41:22.490 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
22:41:22.527 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:41:22.538 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:41:22.559 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:43:11.151 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:43:11.208 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 28512 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
22:43:11.209 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:43:13.397 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:43:14.258 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:43:14.259 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:43:14.260 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:43:14.325 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:43:14.902 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:43:16.234 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:43:16.271 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:43:16.340 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:43:16.380 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:43:17.247 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:43:17.416 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:43:17.452 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:43:23.929 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
22:43:23.931 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
22:43:25.055 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:43:25.069 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:43:25.069 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:43:25.070 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:43:25.070 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:43:25.070 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:43:25.070 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:43:25.070 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bf56981
22:43:26.482 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:43:26.491 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:43:26.500 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 15.764 seconds (process running for 16.999)
22:43:38.231 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:55:40.656 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/************:19736
22:55:40.789 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
23:03:00.407 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
23:03:00.457 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 10212 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
23:03:00.458 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
23:03:02.630 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:03:03.492 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
23:03:03.493 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
23:03:03.494 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
23:03:03.575 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
23:03:04.145 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
23:03:05.511 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
23:03:05.549 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:03:05.615 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
23:03:05.655 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
23:03:06.518 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
23:03:06.694 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
23:03:06.732 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
23:03:13.355 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
23:03:13.358 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
23:03:15.389 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
23:03:15.403 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
23:03:15.405 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
23:03:15.406 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
23:03:15.406 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

23:03:15.407 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
23:03:15.407 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
23:03:15.407 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a4e3cbb
23:03:16.924 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
23:03:16.933 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
23:03:16.943 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 16.964 seconds (process running for 18.189)
23:03:55.750 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:06:45.115 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
23:06:45.271 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
23:06:45.272 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
23:06:45.272 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
23:06:45.273 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
23:06:45.294 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
23:06:45.300 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
23:06:45.310 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
23:06:48.892 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
23:06:48.943 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 29452 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
23:06:48.943 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
23:06:50.943 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:06:51.768 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
23:06:51.770 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
23:06:51.770 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
23:06:51.847 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
23:06:52.380 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
23:06:53.723 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
23:06:53.759 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:06:53.835 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
23:06:53.881 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
23:06:54.727 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
23:06:54.869 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
23:06:54.897 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
23:07:01.528 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
23:07:01.529 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
23:07:02.902 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
23:07:02.915 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
23:07:02.916 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
23:07:02.916 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
23:07:02.917 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

23:07:02.917 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
23:07:02.918 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
23:07:02.918 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@28c076b5
23:07:04.287 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
23:07:04.298 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
23:07:04.307 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 15.78 seconds (process running for 17.192)
23:07:08.806 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:24:59.245 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
23:24:59.300 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 18616 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
23:24:59.301 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
23:25:01.496 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:25:02.355 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
23:25:02.357 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
23:25:02.357 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
23:25:02.423 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
23:25:03.008 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
23:25:04.353 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
23:25:04.413 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:25:04.537 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
23:25:04.606 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
23:25:05.796 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
23:25:06.013 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
23:25:06.054 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
23:25:15.830 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
23:25:15.834 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
23:25:18.150 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
23:25:18.173 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
23:25:18.174 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
23:25:18.175 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
23:25:18.176 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

23:25:18.176 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
23:25:18.177 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
23:25:18.177 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5324647
23:25:19.789 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
23:25:19.803 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
23:25:19.814 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 20.98 seconds (process running for 22.374)
23:25:22.583 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:35:25.385 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
23:35:25.710 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
23:35:25.710 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
23:35:25.711 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
23:35:25.713 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
23:35:25.793 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
23:35:25.817 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
23:35:25.844 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
23:35:30.151 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
23:35:30.208 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 11436 (E:\idea_projects\words\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\words\word-system-service)
23:35:30.209 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
23:35:32.828 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:35:33.786 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
23:35:33.789 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
23:35:33.789 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
23:35:33.859 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
23:35:34.430 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
23:35:35.778 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
23:35:35.818 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:35:35.895 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
23:35:35.935 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
23:35:36.802 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
23:35:36.931 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
23:35:36.961 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
23:35:43.085 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
23:35:43.087 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
23:35:44.184 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
23:35:44.196 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
23:35:44.197 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
23:35:44.197 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
23:35:44.198 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

23:35:44.198 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
23:35:44.198 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
23:35:44.198 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1bed8afd
23:35:45.803 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
23:35:45.816 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
23:35:45.828 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 16.07 seconds (process running for 17.614)
23:35:47.755 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
