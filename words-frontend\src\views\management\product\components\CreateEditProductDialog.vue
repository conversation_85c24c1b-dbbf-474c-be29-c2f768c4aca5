<template>
  <el-dialog 
    :title="title" 
    v-model="dialogVisible" 
    width="600px" 
    append-to-body
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="产品名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入产品名称" />
      </el-form-item>
      <el-form-item label="产品描述" prop="description">
        <el-input v-model="form.description" type="textarea" placeholder="请输入产品描述" />
      </el-form-item>
      <el-form-item label="学科" prop="subject">
        <el-select v-model="form.subject" placeholder="请选择学科">
          <el-option label="英语" value="英语" />
          <el-option label="数学" value="数学" />
          <el-option label="语文" value="语文" />
          <el-option label="物理" value="物理" />
          <el-option label="化学" value="化学" />
        </el-select>
      </el-form-item>
      <el-form-item label="课型" prop="courseType">
        <el-select v-model="form.courseType" placeholder="请选择课型">
          <el-option label="基础课" value="基础课" />
          <el-option label="提升课" value="提升课" />
          <el-option label="专项课" value="专项课" />
          <el-option label="冲刺课" value="冲刺课" />
          <el-option label="思维课" value="思维课" />
          <el-option label="实验课" value="实验课" />
        </el-select>
      </el-form-item>
      <el-form-item label="适用年级" prop="applicableGrades">
        <el-select v-model="form.applicableGrades" multiple placeholder="请选择适用年级">
          <el-option label="小学1年级" value="小学1年级" />
          <el-option label="小学2年级" value="小学2年级" />
          <el-option label="小学3年级" value="小学3年级" />
          <el-option label="小学4年级" value="小学4年级" />
          <el-option label="小学5年级" value="小学5年级" />
          <el-option label="小学6年级" value="小学6年级" />
          <el-option label="初中1年级" value="初中1年级" />
          <el-option label="初中2年级" value="初中2年级" />
          <el-option label="初中3年级" value="初中3年级" />
          <el-option label="高中1年级" value="高中1年级" />
          <el-option label="高中2年级" value="高中2年级" />
          <el-option label="高中3年级" value="高中3年级" />
        </el-select>
      </el-form-item>
      <el-form-item label="单价(元)" prop="unitPrice">
        <el-input-number 
          v-model="unitPriceInYuan" 
          :precision="2" 
          :min="0" 
          placeholder="请输入单价" 
          @change="calculateOriginalPrice" 
        />
      </el-form-item>
      <el-form-item label="数量" prop="quantity">
        <el-input-number 
          v-model="form.quantity" 
          :min="1" 
          placeholder="请输入数量" 
          @change="calculateOriginalPrice" 
        />
      </el-form-item>
      <el-form-item label="赠送课时">
        <el-checkbox v-model="form.hasBonusHours" @change="onBonusHoursChange">
          是否有赠送课时
        </el-checkbox>
      </el-form-item>
      <el-form-item v-if="form.hasBonusHours" label="赠送数量" prop="bonusHoursQuantity">
        <el-input-number v-model="form.bonusHoursQuantity" :min="0" placeholder="请输入赠送课时数量" />
      </el-form-item>
      <el-form-item label="教材费">
        <el-checkbox v-model="form.hasMaterialFee" @change="onMaterialFeeChange">
          是否包含教材费
        </el-checkbox>
      </el-form-item>
      <el-form-item v-if="form.hasMaterialFee" label="教材费(元)" prop="materialFee">
        <el-input-number 
          v-model="materialFeeInYuan" 
          :precision="2" 
          :min="0" 
          placeholder="请输入教材费" 
          @change="calculateOriginalPrice" 
        />
      </el-form-item>
      <el-form-item label="原价(元)">
        <el-input-number v-model="originalPriceInYuan" :precision="2" disabled />
        <span style="color: #999; font-size: 12px; margin-left: 10px;">
          自动计算：单价 × 数量 + 教材费
        </span>
      </el-form-item>
      <el-form-item label="售价(元)" prop="sellingPrice">
        <el-input-number v-model="sellingPriceInYuan" :precision="2" :min="0" placeholder="请输入售价" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="上架">上架</el-radio>
          <el-radio label="下架">下架</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序权重" prop="sortOrder">
        <el-input-number v-model="form.sortOrder" :min="0" placeholder="请输入排序权重" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CreateEditProductDialog">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  createProductApi, 
  updateProductApi, 
  type ProductForm 
} from '@/api/management/product'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

const form = reactive({
  id: undefined,
  name: '',
  description: '',
  subject: '',
  courseType: '',
  applicableGrades: [],
  unitPrice: 0,
  quantity: 1,
  hasBonusHours: false,
  bonusHoursQuantity: 0,
  hasMaterialFee: false,
  materialFee: 0,
  originalPrice: 0,
  sellingPrice: 0,
  status: '上架',
  sortOrder: 0,
  remark: ''
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '产品名称不能为空', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '学科不能为空', trigger: 'change' }
  ],
  applicableGrades: [
    { required: true, message: '适用年级不能为空', trigger: 'change' }
  ],
  unitPrice: [
    { required: true, message: '单价不能为空', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '数量不能为空', trigger: 'blur' }
  ],
  sellingPrice: [
    { required: true, message: '售价不能为空', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const title = computed(() => {
  return form.id ? '修改产品' : '添加产品'
})

const unitPriceInYuan = computed({
  get: () => form.unitPrice ? form.unitPrice / 100 : 0,
  set: (value) => {
    form.unitPrice = Math.round(value * 100)
    calculateOriginalPrice()
  }
})

const materialFeeInYuan = computed({
  get: () => form.materialFee ? form.materialFee / 100 : 0,
  set: (value) => {
    form.materialFee = Math.round(value * 100)
    calculateOriginalPrice()
  }
})

const originalPriceInYuan = computed({
  get: () => form.originalPrice ? form.originalPrice / 100 : 0,
  set: (value) => {
    form.originalPrice = Math.round(value * 100)
  }
})

const sellingPriceInYuan = computed({
  get: () => form.sellingPrice ? form.sellingPrice / 100 : 0,
  set: (value) => {
    form.sellingPrice = Math.round(value * 100)
  }
})

// 方法
const calculateOriginalPrice = () => {
  if (form.unitPrice && form.quantity) {
    const basePrice = form.unitPrice * form.quantity
    const materialFee = form.hasMaterialFee ? form.materialFee : 0
    form.originalPrice = basePrice + materialFee
  } else {
    form.originalPrice = 0
  }
}

const onBonusHoursChange = (value) => {
  if (!value) {
    form.bonusHoursQuantity = 0
  }
}

const onMaterialFeeChange = (value) => {
  if (!value) {
    form.materialFee = 0
  }
  calculateOriginalPrice()
}

const resetForm = () => {
  Object.assign(form, {
    id: undefined,
    name: '',
    description: '',
    subject: '',
    courseType: '',
    applicableGrades: [],
    unitPrice: 0,
    quantity: 1,
    hasBonusHours: false,
    bonusHoursQuantity: 0,
    hasMaterialFee: false,
    materialFee: 0,
    originalPrice: 0,
    sellingPrice: 0,
    status: '上架',
    sortOrder: 0,
    remark: ''
  })
  formRef.value?.clearValidate()
}

const handleCancel = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    if (form.id) {
      await updateProductApi(form)
      ElMessage.success('修改成功')
    } else {
      await createProductApi(form)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(form.id ? '修改失败' : '新增失败')
    }
  } finally {
    submitting.value = false
  }
}

// 监听产品数据变化
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    Object.assign(form, newProduct)
  } else {
    resetForm()
  }
}, { immediate: true })
</script>
