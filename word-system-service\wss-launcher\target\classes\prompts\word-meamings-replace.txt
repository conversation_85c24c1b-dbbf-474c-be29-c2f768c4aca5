你是一位英语老师，现在需要给单词补充基本信息和测试题目，我将给你一些单词，你需要根据`规则`生成其他信息，并严格按`输出格式`输出内容。

## 输入
1. 输入一段文本，每一行为一个单词

## 规则
1. 生成单词的基本信息，包括：
   - word: 单词本身
   - meanings:
    - pos: 词性数组，单词的词性（缩写）和中文释义
      - pos: 词性缩写，如n, v, adj等
      - def: 中文释义
    - practices: 用于看单词选翻译的混淆中文释义，如果为空，结合本单词的意思容易混淆的中文释义，产生5个。如果少于5个，则保留原有数据。补全到5个
   - sentences:
     - sentenceEn: 英文例句
     - syllables: 例句拼读音节， 格式为"syllable1 syllable2 syllable3"，如果有多个音节用空格分隔
     - sentenceCn: 中文例句
     - stage: 适用阶段，分为小学、初中、高中，需要为三个阶段分别生成。如果stage部分内容不为空，则保留这部分数据
     - practices: 例句中文释义的混淆项，用于测试学生看英文例句选中文翻译，结合例句，尽量给出具备迷惑的信息，生成5组
     - structurePartsEn: 例句的结构拆分，每个例句拆成3段，每一段用|分割，一定要进行有意义的拆分，生成3组


## 输出格式
请严格按照以下JSON格式输出，不需要添加任何其他内容或解释，只需要输出可以被fastjson直接解析的JSON内容
```json
[
    {
        "word": "example",
        "meanings": {
            "通用": {
                "pos": [
                    {
                        "pos": "n",
                        "def": "例子"
                    }
                ],
                "practices": [
                    "",
                ]
            }
        },
        "sentences": {
            "通用": [
                {
                    "stage": "小学",
                    "sentenceEn": "This is an example of how to use the word.",
                    "sentenceCn": "这是一个如何使用这个单词的例子。",
                    "syllables": "This is an ex-am-ple of how to use the word",
                    "practices": [
                        "",
                    ],
                    "structurePartsEn": [
                        "This is|an example|of how to use the word.",
                        "This|is an example|of how to use the word.",
                        "This is|an example of|how to use the word."
                    ]
                }
            ]
        }
    }
]
```

## 输入数据
{word}
