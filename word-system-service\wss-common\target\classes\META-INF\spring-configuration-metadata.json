{"groups": [{"name": "aliyun.oss", "type": "org.nonamespace.word.common.config.AliyunOssConfig", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "thread", "type": "org.nonamespace.word.common.config.ThreadPoolPropertiesConfig", "sourceType": "org.nonamespace.word.common.config.ThreadPoolPropertiesConfig"}, {"name": "wx.mp", "type": "org.nonamespace.word.common.config.WxMpProperties", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}], "properties": [{"name": "aliyun.oss.access-key-id", "type": "java.lang.String", "description": "访问OSS的AccessKeyId", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "aliyun.oss.access-key-secret", "type": "java.lang.String", "description": "访问OSS的AccessKeySecret", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "aliyun.oss.bucket-name", "type": "java.lang.String", "description": "存储空间名称", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "aliyun.oss.endpoint", "type": "java.lang.String", "description": "OSS服务的Endpoint", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "aliyun.oss.url-prefix", "type": "java.lang.String", "description": "访问文件的URL前缀", "sourceType": "org.nonamespace.word.common.config.AliyunOssConfig"}, {"name": "thread.core-pool-size", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.common.config.ThreadPoolPropertiesConfig"}, {"name": "thread.keep-alive-time", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.common.config.ThreadPoolPropertiesConfig"}, {"name": "thread.maximum-pool-size", "type": "java.lang.Integer", "sourceType": "org.nonamespace.word.common.config.ThreadPoolPropertiesConfig"}, {"name": "wx.mp.configs", "type": "java.util.List<org.nonamespace.word.common.config.WxMpProperties$MpConfig>", "description": "多个公众号配置信息", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}, {"name": "wx.mp.learning-report-detail-url", "type": "java.lang.String", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}, {"name": "wx.mp.redirect-uri", "type": "java.lang.String", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}, {"name": "wx.mp.review-report-detail-url", "type": "java.lang.String", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}, {"name": "wx.mp.use-redis", "type": "java.lang.Bo<PERSON>an", "description": "是否使用redis存储access token", "sourceType": "org.nonamespace.word.common.config.WxMpProperties"}], "hints": []}