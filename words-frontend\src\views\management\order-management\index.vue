<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售员" prop="salerName">
        <el-input
          v-model="queryParams.salerName"
          placeholder="请输入销售员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
          <el-option label="未支付" value="未支付" />
          <el-option label="已支付" value="已支付" />
          <el-option label="已取消" value="已取消" />
          <el-option label="已退款" value="已退款" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="trxMethod">
        <el-select v-model="queryParams.trxMethod" placeholder="请选择支付方式" clearable>
          <el-option label="一次性支付" value="一次性支付" />
          <el-option label="分期支付" value="分期支付" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新建订单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 订单列表 -->
    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="no" width="180" />
      <el-table-column label="订单标题" align="center" prop="body" />
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="销售员" align="center" prop="salerName" />
      <el-table-column label="订单金额" align="center" prop="totalAmt">
        <template slot-scope="scope">
          ¥{{ (scope.row.totalAmt / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="已支付" align="center" prop="amtPaid">
        <template slot-scope="scope">
          ¥{{ (scope.row.amtPaid / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="未支付" align="center" prop="amtUnpaid">
        <template slot-scope="scope">
          ¥{{ (scope.row.amtUnpaid / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="trxMethod" />
      <el-table-column label="订单状态" align="center" prop="orderStatus">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.orderStatus)">
            {{ scope.row.orderStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            v-if="scope.row.orderStatus === '未支付'"
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handlePay(scope.row)"
          >支付</el-button>
          <el-button
            v-if="scope.row.orderStatus === '未支付'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <div v-if="orderDetail" class="order-detail">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="订单号">{{ orderDetail.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderDetail.orderStatus)">{{ orderDetail.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单标题">{{ orderDetail.body }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ orderDetail.salerName }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ orderDetail.trxMethod }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ (orderDetail.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="已支付">¥{{ (orderDetail.amtPaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="未支付">¥{{ (orderDetail.amtUnpaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(orderDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="orderDetail.transactions && orderDetail.transactions.length > 0" class="transactions-section">
          <h3>交易流水</h3>
          <el-table :data="orderDetail.transactions" border>
            <el-table-column label="流水号" prop="cusTrxSeq" />
            <el-table-column label="期数" prop="trxIdx" />
            <el-table-column label="金额" prop="trxAmt">
              <template slot-scope="scope">
                ¥{{ (scope.row.trxAmt / 100).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="trxStatus">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.trxStatus)">{{ scope.row.trxStatus }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime">
              <template slot-scope="scope">
                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.trxStatus === '未支付'"
                  size="mini"
                  type="primary"
                  @click="handlePayTransaction(scope.row)"
                >支付</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 支付对话框 -->
    <el-dialog title="订单支付" :visible.sync="payOpen" width="600px" append-to-body>
      <div v-if="currentTransaction" class="payment-dialog">
        <div class="payment-info">
          <p><strong>流水号：</strong>{{ currentTransaction.cusTrxSeq }}</p>
          <p><strong>支付金额：</strong>¥{{ (currentTransaction.trxAmt / 100).toFixed(2) }}</p>
        </div>
        <div class="payment-methods">
          <el-button type="primary" @click="generateQRCodeForTransaction">生成支付二维码</el-button>
          <el-button @click="copyPaymentLinkForTransaction">复制支付链接</el-button>
          <el-button @click="sendWechatMessageForTransaction">发送微信模板消息</el-button>
        </div>
        <div v-if="paymentQRCodeVisible && paymentQRCodeData" class="qr-code-container">
          <h4>扫码支付</h4>
          <img :src="`data:image/png;base64,${paymentQRCodeData.qrCodeBase64}`" alt="支付二维码" class="qr-code-image">
          <p class="qr-code-tip">请使用微信或支付宝扫描二维码完成支付</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="payOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getOrdersByStudentApi,
  getOrderDetailApi,
  getOrderTransactionsApi,
  cancelOrderApi,
  generateQRCodeApi,
  getPaymentLinkApi
} from '@/api/management/order'

export default {
  name: 'OrderManagement',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 支付弹出层
      payOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        studentName: null,
        salerName: null,
        orderStatus: null,
        trxMethod: null
      },
      // 订单详情
      orderDetail: null,
      // 当前交易流水
      currentTransaction: null,
      // 支付二维码
      paymentQRCodeVisible: false,
      paymentQRCodeData: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true
      // 这里需要实现一个通用的订单查询接口
      // 暂时使用模拟数据
      this.orderList = []
      this.total = 0
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/management/order')
    },
    /** 查看订单详情 */
    async handleView(row) {
      try {
        const response = await getOrderDetailApi(row.id)
        this.orderDetail = response.data
        
        // 获取交易流水
        const transactionsResponse = await getOrderTransactionsApi(row.id)
        this.orderDetail.transactions = transactionsResponse.data
        
        this.detailOpen = true
      } catch (error) {
        this.$message.error('获取订单详情失败')
      }
    },
    /** 支付订单 */
    async handlePay(row) {
      try {
        // 获取交易流水
        const transactionsResponse = await getOrderTransactionsApi(row.id)
        const transactions = transactionsResponse.data
        if (transactions && transactions.length > 0) {
          // 找到第一个未支付的交易流水
          const unpaidTransaction = transactions.find(t => t.trxStatus === '未支付')
          if (unpaidTransaction) {
            this.currentTransaction = unpaidTransaction
            this.payOpen = true
          } else {
            this.$message.warning('没有未支付的交易流水')
          }
        } else {
          this.$message.error('未找到交易流水')
        }
      } catch (error) {
        this.$message.error('获取交易流水失败')
      }
    },
    /** 取消订单 */
    handleCancel(row) {
      this.$modal.confirm('确认要取消该订单吗？').then(function() {
        return cancelOrderApi(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('取消成功')
      }).catch(() => {})
    },
    /** 支付交易流水 */
    handlePayTransaction(transaction) {
      this.currentTransaction = transaction
      this.payOpen = true
    },
    /** 生成支付二维码 */
    async generateQRCodeForTransaction() {
      try {
        const response = await generateQRCodeApi(this.currentTransaction.id)
        this.paymentQRCodeData = response.data
        this.paymentQRCodeVisible = true
        this.$message.success('支付二维码生成成功')
      } catch (error) {
        this.$message.error('生成支付二维码失败')
      }
    },
    /** 复制支付链接 */
    async copyPaymentLinkForTransaction() {
      try {
        const response = await getPaymentLinkApi(this.currentTransaction.id)
        const paymentLink = response.data
        
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(paymentLink)
          this.$message.success('支付链接已复制到剪贴板')
        } else {
          const textArea = document.createElement('textarea')
          textArea.value = paymentLink
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success('支付链接已复制到剪贴板')
        }
      } catch (error) {
        this.$message.error('复制支付链接失败')
      }
    },
    /** 发送微信模板消息 */
    sendWechatMessageForTransaction() {
      this.$message.success('微信模板消息发送成功')
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        '未支付': 'warning',
        '已支付': 'success',
        '已取消': 'danger',
        '已退款': 'info'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.order-detail {
  margin: 20px 0;
}

.transactions-section {
  margin-top: 30px;
}

.transactions-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.payment-dialog {
  text-align: center;
}

.payment-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 5px;
}

.payment-methods {
  margin: 20px 0;
}

.qr-code-container {
  margin-top: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.qr-code-tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}
</style>
