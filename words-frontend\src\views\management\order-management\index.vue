<template>
  <div class="order-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售员">
          <el-input
            v-model="searchForm.salerName"
            placeholder="请输入销售员姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="请选择订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未支付" value="未支付" />
            <el-option label="已支付" value="已支付" />
            <el-option label="已取消" value="已取消" />
            <el-option label="已退款" value="已退款" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select
            v-model="searchForm.trxMethod"
            placeholder="请选择支付方式"
            clearable
            style="width: 150px"
          >
            <el-option label="一次性支付" value="一次性支付" />
            <el-option label="分期支付" value="分期支付" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="orderList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单号" prop="no" width="180" />
        <el-table-column label="订单标题" prop="body" min-width="200" show-overflow-tooltip />
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="销售员" prop="salerName" width="120" />
        <el-table-column label="订单金额" prop="totalAmt" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.totalAmt / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="已支付" prop="amtPaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtPaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="未支付" prop="amtUnpaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtUnpaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="支付方式" prop="trxMethod" width="120" align="center" />
        <el-table-column label="订单状态" prop="orderStatus" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)" size="small">
              {{ row.orderStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="240" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handleView(row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="row.orderStatus === '未支付'"
            type="success"
            link
            @click="handlePay(row)"
          >
            支付
          </el-button>
          <el-button
            v-if="row.orderStatus === '未支付'"
            type="danger"
            link
            @click="handleCancel(row)"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <div v-if="orderDetail" class="order-detail">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="订单号">{{ orderDetail.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderDetail.orderStatus)">{{ orderDetail.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单标题">{{ orderDetail.body }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ orderDetail.salerName }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ orderDetail.trxMethod }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ (orderDetail.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="已支付">¥{{ (orderDetail.amtPaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="未支付">¥{{ (orderDetail.amtUnpaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(orderDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="orderDetail.transactions && orderDetail.transactions.length > 0" class="transactions-section">
          <h3>交易流水</h3>
          <el-table :data="orderDetail.transactions" border>
            <el-table-column label="流水号" prop="cusTrxSeq" />
            <el-table-column label="期数" prop="trxIdx" />
            <el-table-column label="金额" prop="trxAmt">
              <template #default="{ row }">
                ¥{{ (row.trxAmt / 100).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="trxStatus">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.trxStatus)">{{ row.trxStatus }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime">
              <template #default="{ row }">
                {{ parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button
                  v-if="row.trxStatus === '未支付'"
                  size="mini"
                  type="primary"
                  @click="handlePayTransaction(row)"
                >支付</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 支付对话框 -->
    <el-dialog title="订单支付" :visible.sync="payOpen" width="600px" append-to-body>
      <div v-if="currentTransaction" class="payment-dialog">
        <div class="payment-info">
          <p><strong>流水号：</strong>{{ currentTransaction.cusTrxSeq }}</p>
          <p><strong>支付金额：</strong>¥{{ (currentTransaction.trxAmt / 100).toFixed(2) }}</p>
        </div>
        <div class="payment-methods">
          <el-button type="primary" @click="generateQRCodeForTransaction">生成支付二维码</el-button>
          <el-button @click="copyPaymentLinkForTransaction">复制支付链接</el-button>
          <el-button @click="sendWechatMessageForTransaction">发送微信模板消息</el-button>
        </div>
        <div v-if="paymentQRCodeVisible && paymentQRCodeData" class="qr-code-container">
          <h4>扫码支付</h4>
          <img :src="`data:image/png;base64,${paymentQRCodeData.qrCodeBase64}`" alt="支付二维码" class="qr-code-image">
          <p class="qr-code-tip">请使用微信或支付宝扫描二维码完成支付</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="payOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="OrderManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import {
  getOrdersByStudentApi,
  getOrderDetailApi,
  getOrderTransactionsApi,
  cancelOrderApi,
  generateQRCodeApi,
  getPaymentLinkApi
} from '@/api/management/order'

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const detailOpen = ref(false)
const payOpen = ref(false)
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  studentName: '',
  salerName: '',
  orderStatus: '',
  trxMethod: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const orderDetail = ref(null)
const currentTransaction = ref(null)
const paymentQRCodeVisible = ref(false)
const paymentQRCodeData = ref(null)

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    // 这里需要实现一个通用的订单查询接口
    // 暂时使用模拟数据
    orderList.value = []
    pagination.total = 0
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    studentName: '',
    salerName: '',
    orderStatus: '',
    trxMethod: ''
  })
  dateRange.value = []
  handleSearch()
}

const handleSelectionChange = (selection) => {
  // 处理选择变化
}

const handleAdd = () => {
  // 跳转到订单创建页面
  window.open('/management/order', '_blank')
}

const handleView = async (row) => {
  try {
    const response = await getOrderDetailApi(row.id)
    orderDetail.value = response.data

    // 获取交易流水
    const transactionsResponse = await getOrderTransactionsApi(row.id)
    orderDetail.value.transactions = transactionsResponse.data

    detailOpen.value = true
  } catch (error) {
    ElMessage.error('获取订单详情失败')
  }
}

const handlePay = async (row) => {
  try {
    // 获取交易流水
    const transactionsResponse = await getOrderTransactionsApi(row.id)
    const transactions = transactionsResponse.data
    if (transactions && transactions.length > 0) {
      // 找到第一个未支付的交易流水
      const unpaidTransaction = transactions.find(t => t.trxStatus === '未支付')
      if (unpaidTransaction) {
        currentTransaction.value = unpaidTransaction
        payOpen.value = true
      } else {
        ElMessage.warning('没有未支付的交易流水')
      }
    } else {
      ElMessage.error('未找到交易流水')
    }
  } catch (error) {
    ElMessage.error('获取交易流水失败')
  }
}

const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm('确认要取消该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await cancelOrderApi(row.id)
    ElMessage.success('取消成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
    }
  }
}

const handlePayTransaction = (transaction) => {
  currentTransaction.value = transaction
  payOpen.value = true
}

const generateQRCodeForTransaction = async () => {
  try {
    const response = await generateQRCodeApi(currentTransaction.value.id)
    paymentQRCodeData.value = response.data
    paymentQRCodeVisible.value = true
    ElMessage.success('支付二维码生成成功')
  } catch (error) {
    ElMessage.error('生成支付二维码失败')
  }
}

const copyPaymentLinkForTransaction = async () => {
  try {
    const response = await getPaymentLinkApi(currentTransaction.value.id)
    const paymentLink = response.data

    if (navigator.clipboard) {
      await navigator.clipboard.writeText(paymentLink)
      ElMessage.success('支付链接已复制到剪贴板')
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = paymentLink
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('支付链接已复制到剪贴板')
    }
  } catch (error) {
    ElMessage.error('复制支付链接失败')
  }
}

const sendWechatMessageForTransaction = () => {
  ElMessage.success('微信模板消息发送成功')
}

const getStatusType = (status) => {
  const statusMap = {
    '未支付': 'warning',
    '已支付': 'success',
    '已取消': 'danger',
    '已退款': 'info'
  }
  return statusMap[status] || 'info'
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.order-detail {
  margin: 20px 0;
}

.transactions-section {
  margin-top: 30px;
}

.transactions-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.payment-dialog {
  text-align: center;
}

.payment-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 5px;
}

.payment-methods {
  margin: 20px 0;
}

.qr-code-container {
  margin-top: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.qr-code-tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

/* 新增统一样式 */
.order-management-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
