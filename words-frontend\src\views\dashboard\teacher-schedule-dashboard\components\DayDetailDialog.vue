<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1200px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="day-detail-content">
      <!-- 日期信息 -->
      <div class="date-info">
        <el-tag type="success" size="large">{{ formatDate(date) }}</el-tag>
        <span class="weekday">{{ getWeekdayName(date) }}</span>
      </div>

      <!-- 统计概览 -->
      <div class="summary-stats" v-if="dayData">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-value">{{ dayData.totalTeachers }}</div>
              <div class="summary-label">总教师数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-value">{{ dayData.availableTeachers }}</div>
              <div class="summary-label">有课时教师</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-value">{{ dayData.totalSlots }}</div>
              <div class="summary-label">总可排课课次</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-value">{{ dayData.averageSlots }}</div>
              <div class="summary-label">平均课次</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" inline size="small">
          <el-form-item label="教学组">
            <el-select
              v-model="filterForm.groupId"
              placeholder="全部教学组"
              clearable
              style="width: 150px"
              @change="loadTeacherData"
            >
              <el-option
                v-for="group in teachingGroups"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="搜索教师">
            <el-input
              v-model="filterForm.keyword"
              placeholder="教师姓名或手机号"
              clearable
              style="width: 200px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="课次筛选">
            <el-select
              v-model="filterForm.slotsFilter"
              placeholder="全部"
              clearable
              style="width: 120px"
              @change="applyFilter"
            >
              <el-option label="有课时(>0)" value="hasSlots" />
              <el-option label="无课时(=0)" value="noSlots" />
              <el-option label="课时较多(≥5)" value="manySlots" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 教师列表 -->
      <div class="teacher-list">
        <el-table
          :data="filteredTeachers"
          stripe
          border
          height="400"
          v-loading="loading"
        >
          <el-table-column prop="teacherName" label="教师姓名" width="120" fixed="left">
            <template #default="{ row }">
              <div class="teacher-info">
                <el-avatar :size="32" :src="row.avatar">
                  {{ row.teacherName?.charAt(0) }}
                </el-avatar>
                <span class="teacher-name">{{ row.teacherName }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="phone" label="手机号" width="130">
            <template #default="{ row }">
              <span class="phone-number">{{ row.phone || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="groupName" label="教学组" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.groupName || '-' }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="availableSlots" label="可排课课次" width="120" sortable>
            <template #default="{ row }">
              <el-tag 
                :type="getSlotsTagType(row.availableSlots)" 
                size="default"
                effect="dark"
              >
                {{ row.availableSlots }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="时间段分布" min-width="200">
            <template #default="{ row }">
              <div class="time-slots" v-if="row.timeSlots && row.timeSlots.length > 0">
                <el-tag
                  v-for="slot in row.timeSlots"
                  :key="`${slot.startTime}-${slot.endTime}`"
                  size="small"
                  class="time-slot-tag"
                >
                  {{ slot.startTime }}-{{ slot.endTime }}
                </el-tag>
              </div>
              <span v-else class="no-slots">暂无可用时间段</span>
            </template>
          </el-table-column>

          <el-table-column label="已排课程" width="100">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="viewScheduledCourses(row)"
                :disabled="!row.scheduledCourses || row.scheduledCourses.length === 0"
              >
                {{ row.scheduledCourses?.length || 0 }}个课程
              </el-button>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewTeacherSchedule(row)">
                查看排课
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="exportData" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 已排课程详情对话框 -->
    <ScheduledCoursesDialog
      v-model="coursesDialogVisible"
      :teacher="selectedTeacher"
      :date="date"
    />

    <!-- 教师排课详情对话框 -->
    <TeacherScheduleDialog
      v-model="scheduleDialogVisible"
      :teacher="selectedTeacher"
      :date="date"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import ScheduledCoursesDialog from './ScheduledCoursesDialog.vue'
import TeacherScheduleDialog from './TeacherScheduleDialog.vue'
import { teacherScheduleDashboardApi } from '@/api/management/teacher-schedule-dashboard'
import { teachingGroupApi } from '@/api/management/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  date: {
    type: String,
    required: true
  },
  groupIds: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const teacherData = ref([])
const teachingGroups = ref([])
const dayData = ref(null)
const coursesDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const selectedTeacher = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 筛选表单
const filterForm = reactive({
  groupId: '',
  keyword: '',
  slotsFilter: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return `${formatDate(props.date)} 可排课详情`
})

const filteredTeachers = computed(() => {
  let filtered = [...teacherData.value]

  // 教学组筛选
  if (filterForm.groupId) {
    filtered = filtered.filter(teacher => teacher.groupId === filterForm.groupId)
  }

  // 关键词搜索
  if (filterForm.keyword) {
    const keyword = filterForm.keyword.toLowerCase()
    filtered = filtered.filter(teacher => 
      teacher.teacherName?.toLowerCase().includes(keyword) ||
      teacher.phone?.includes(keyword)
    )
  }

  // 课次筛选
  if (filterForm.slotsFilter) {
    switch (filterForm.slotsFilter) {
      case 'hasSlots':
        filtered = filtered.filter(teacher => teacher.availableSlots > 0)
        break
      case 'noSlots':
        filtered = filtered.filter(teacher => teacher.availableSlots === 0)
        break
      case 'manySlots':
        filtered = filtered.filter(teacher => teacher.availableSlots >= 5)
        break
    }
  }

  // 更新总数
  total.value = filtered.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getWeekdayName = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  return weekdays[date.getDay()]
}

const getSlotsTagType = (slots) => {
  if (slots >= 5) return 'success'
  if (slots >= 3) return 'warning'
  if (slots >= 1) return 'info'
  return 'danger'
}

const loadTeachingGroups = async () => {
  try {
    const response = await teachingGroupApi.getTeachingGroups({ pageNum: 1, pageSize: 1000 })
    teachingGroups.value = response.data?.records || []
  } catch (error) {
    console.error('加载教学组失败:', error)
  }
}

const loadTeacherData = async () => {
  if (!props.date) return

  loading.value = true
  try {
    // 这里调用API获取指定日期的教师可排课数据
    const response = await teacherScheduleDashboardApi.getDayTeacherDetail({
      date: props.date,
      groupIds: props.groupIds
    })

    teacherData.value = response.data?.teachers || []
    dayData.value = response.data?.summary || null

    // 重置分页
    currentPage.value = 1
  } catch (error) {
    ElMessage.error('加载教师数据失败: ' + error.message)
    teacherData.value = []
    dayData.value = null
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const applyFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const viewScheduledCourses = (teacher) => {
  selectedTeacher.value = teacher
  coursesDialogVisible.value = true
}

const viewTeacherSchedule = (teacher) => {
  selectedTeacher.value = teacher
  scheduleDialogVisible.value = true
}

const exportData = async () => {
  exporting.value = true
  try {
    // 这里实现数据导出功能
    ElMessage.info('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    exporting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  // 重置筛选条件
  filterForm.groupId = ''
  filterForm.keyword = ''
  filterForm.slotsFilter = ''
  currentPage.value = 1
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadTeacherData()
  }
})

watch(() => props.date, () => {
  if (props.modelValue) {
    loadTeacherData()
  }
})

// 生命周期
onMounted(() => {
  loadTeachingGroups()
})
</script>

<style lang="scss" scoped>
.day-detail-content {
  .date-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    .weekday {
      font-size: 16px;
      color: #606266;
      font-weight: 500;
    }
  }

  .summary-stats {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .summary-item {
      text-align: center;

      .summary-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .summary-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .teacher-list {
    .teacher-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .teacher-name {
        font-weight: 500;
      }
    }

    .phone-number {
      font-family: 'Courier New', monospace;
      color: #606266;
    }

    .time-slots {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .time-slot-tag {
        margin: 0;
      }
    }

    .no-slots {
      color: #c0c4cc;
      font-style: italic;
    }
  }

  .pagination-wrapper {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
