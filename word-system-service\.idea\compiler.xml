<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for word-system-service" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="D:/maven_repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="D:/maven_repository/org/springframework/boot/spring-boot-configuration-processor/3.3.0/spring-boot-configuration-processor-3.3.0.jar" />
        </processorPath>
        <module name="wss-common" />
        <module name="wss-launcher" />
        <module name="wss-wechat" />
        <module name="wss-thirdpart" />
        <module name="wss-server" />
        <module name="wss-openai" />
        <module name="wss-rest" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="wss-pay" target="21" />
      <module name="wxx-openai" target="21" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="word-system-service" options="-parameters" />
      <module name="wss-common" options="-parameters" />
      <module name="wss-launcher" options="-parameters" />
      <module name="wss-openai" options="-parameters" />
      <module name="wss-pay" options="-parameters" />
      <module name="wss-rest" options="-parameters" />
      <module name="wss-server" options="-parameters" />
      <module name="wss-thirdpart" options="-parameters" />
      <module name="wss-wechat" options="-parameters" />
      <module name="wxx-openai" options="-parameters" />
    </option>
  </component>
</project>