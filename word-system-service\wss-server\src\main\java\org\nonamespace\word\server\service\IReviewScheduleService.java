package org.nonamespace.word.server.service;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.ReviewSchedule;
import org.nonamespace.word.server.dto.ReviewScheduleQueryDto;
import org.nonamespace.word.server.dto.ReviewScheduleUploadDto;

/**
 * 抗遗忘复习计划Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IReviewScheduleService extends IService<ReviewSchedule>
{

    void generateReviewSchedules(String studentId, String courseId, Date date,  List<String> textbookItemIds);

    /**
     * 分页查询抗遗忘复习计划
     *
     * @param req 查询条件
     * @return 分页结果
     */
    Page<ReviewScheduleQueryDto.Resp> queryPage(ReviewScheduleQueryDto.Req req);

    /**
     * 查询抗遗忘复习计划列表
     *
     * @param req 查询条件
     * @return 复习计划列表
     */
    List<ReviewScheduleQueryDto.Resp> queryList(ReviewScheduleQueryDto.Req req);

    /**
     * 上传抗遗忘复习完成情况（图片和说明）
     *
     * @param req 上传请求参数
     * @return 上传响应结果
     */
    ReviewScheduleUploadDto.UploadResp uploadReviewCompletion(ReviewScheduleUploadDto.UploadReq req);

    /**
     * 查看抗遗忘复习上传内容
     *
     * @param req 查看请求参数
     * @return 上传内容详情
     */
    ReviewScheduleUploadDto.ViewResp viewUploadedContent(ReviewScheduleUploadDto.ViewReq req);

}
