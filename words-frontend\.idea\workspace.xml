<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="eeaeb575-0a05-47f7-87a8-ff42f672fd32" name="Changes" comment="feat: 订单管理">
      <change beforePath="$PROJECT_DIR$/src/views/management/product/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/management/product/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/management/product/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Vue Composition API Component" />
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xB0L0zJdVxjq7uFQzjM6N2AsUC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;features/v1.0.0&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/idea_projects/words/words-frontend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build:prod.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Debug&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\JetBrains\\IntelliJ IDEA 2024.3.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\idea_projects\words-frontend\src\views\course\components" />
      <recent name="E:\idea_projects\words-frontend\src\api\course" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="build:prod" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:prod" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <package-manager value="yarn" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build:prod" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.25659.39" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.25659.39" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="eeaeb575-0a05-47f7-87a8-ff42f672fd32" name="Changes" comment="" />
      <created>1747396099622</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747396099622</updated>
      <workItem from="1747396100797" duration="1510000" />
      <workItem from="1747408294151" duration="1999000" />
      <workItem from="1747448030022" duration="12071000" />
      <workItem from="1747490544045" duration="721000" />
      <workItem from="1747491507229" duration="2657000" />
      <workItem from="1747497130183" duration="3669000" />
      <workItem from="1747502201262" duration="1730000" />
      <workItem from="1747504038854" duration="580000" />
      <workItem from="1747533659153" duration="4313000" />
      <workItem from="1747540245566" duration="1374000" />
      <workItem from="1747553301430" duration="2109000" />
      <workItem from="1747572980661" duration="34000" />
      <workItem from="1747575542698" duration="405000" />
      <workItem from="1747654852644" duration="2414000" />
      <workItem from="1747669915645" duration="702000" />
      <workItem from="1747740901346" duration="2050000" />
      <workItem from="1747745392177" duration="146000" />
      <workItem from="1747745956243" duration="6283000" />
      <workItem from="1747761003974" duration="3000" />
      <workItem from="1747830555396" duration="1950000" />
      <workItem from="1747833219266" duration="51000" />
      <workItem from="1747833479336" duration="69000" />
      <workItem from="1747833558072" duration="2058000" />
      <workItem from="1747835661625" duration="1093000" />
      <workItem from="1747836775213" duration="642000" />
      <workItem from="1747837522404" duration="2372000" />
      <workItem from="1747841723888" duration="1098000" />
      <workItem from="1747869653098" duration="33000" />
      <workItem from="1747871895687" duration="21000" />
      <workItem from="1747916134611" duration="3300000" />
      <workItem from="1747920024466" duration="1225000" />
      <workItem from="1748089975821" duration="11813000" />
      <workItem from="1748138442320" duration="1334000" />
      <workItem from="1748141323992" duration="23204000" />
      <workItem from="1748187687073" duration="8000" />
      <workItem from="1748261173914" duration="11954000" />
      <workItem from="1748273326083" duration="1367000" />
      <workItem from="1748276157846" duration="384000" />
      <workItem from="1748312850996" duration="5418000" />
      <workItem from="1748346096771" duration="18037000" />
      <workItem from="1748430846893" duration="8743000" />
      <workItem from="1748490060226" duration="10974000" />
      <workItem from="1748534784371" duration="1036000" />
      <workItem from="1748610861686" duration="14385000" />
      <workItem from="1748626503797" duration="3750000" />
      <workItem from="1748678663488" duration="22609000" />
      <workItem from="1748839352978" duration="1607000" />
      <workItem from="1748858573617" duration="9693000" />
      <workItem from="1748878884906" duration="1513000" />
      <workItem from="1748953617298" duration="1817000" />
      <workItem from="1748957250267" duration="764000" />
      <workItem from="1748958154262" duration="8701000" />
      <workItem from="1748967190178" duration="985000" />
      <workItem from="1749039097197" duration="487000" />
      <workItem from="1749135000409" duration="2201000" />
      <workItem from="1749209628497" duration="50000" />
      <workItem from="1749209791968" duration="3909000" />
      <workItem from="1749214013071" duration="6938000" />
      <workItem from="1749222331104" duration="639000" />
      <workItem from="1749305737891" duration="4021000" />
      <workItem from="1749350743464" duration="1214000" />
      <workItem from="1749477035443" duration="6152000" />
      <workItem from="1749558914658" duration="79000" />
      <workItem from="1749564424658" duration="3891000" />
      <workItem from="1749643189234" duration="9494000" />
      <workItem from="1749653479710" duration="1686000" />
      <workItem from="1749817477010" duration="6105000" />
      <workItem from="1749873281365" duration="15890000" />
      <workItem from="1749918905173" duration="2635000" />
      <workItem from="1749993220177" duration="8456000" />
      <workItem from="1750079362675" duration="2797000" />
      <workItem from="1750166441797" duration="309000" />
      <workItem from="1750426760519" duration="1660000" />
      <workItem from="1750477286135" duration="38000" />
      <workItem from="1750558258486" duration="3223000" />
      <workItem from="1751075355072" duration="281000" />
      <workItem from="1751167289300" duration="1451000" />
      <workItem from="1751542014262" duration="193000" />
      <workItem from="1751548910595" duration="696000" />
      <workItem from="1751586377826" duration="599000" />
      <workItem from="1751641062159" duration="843000" />
      <workItem from="1751974212695" duration="1343000" />
      <workItem from="1752238894935" duration="1196000" />
      <workItem from="1752904484868" duration="1267000" />
      <workItem from="1753787363344" duration="4017000" />
      <workItem from="1754133214026" duration="8248000" />
      <workItem from="1754186858189" duration="132000" />
      <workItem from="1754312968845" duration="6583000" />
      <workItem from="1754322381423" duration="115000" />
      <workItem from="1754322737802" duration="24000" />
      <workItem from="1754400198393" duration="59000" />
      <workItem from="1754400294354" duration="6589000" />
    </task>
    <task id="LOCAL-00068" summary="fix：调整多tab页面，localstorage冲突问题">
      <option name="closed" value="true" />
      <created>1748630199080</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1748630199081</updated>
    </task>
    <task id="LOCAL-00069" summary="feat：单词测验页面，增加这个单词不认识按钮">
      <option name="closed" value="true" />
      <created>1748698612782</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1748698612782</updated>
    </task>
    <task id="LOCAL-00070" summary="fix：积分奖励下拉，延迟隐藏">
      <option name="closed" value="true" />
      <created>1748702299298</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1748702299298</updated>
    </task>
    <task id="LOCAL-00071" summary="fix：修改标记不认识词汇提示">
      <option name="closed" value="true" />
      <created>1748706363165</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1748706363165</updated>
    </task>
    <task id="LOCAL-00072" summary="feat：添加D4单词测验，播放音标选择中英文阶段">
      <option name="closed" value="true" />
      <created>1748708140205</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1748708140205</updated>
    </task>
    <task id="LOCAL-00073" summary="feat：添加D4单词测验，播放音标选择中英文阶段">
      <option name="closed" value="true" />
      <created>1748708392445</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1748708392445</updated>
    </task>
    <task id="LOCAL-00074" summary="feat：添加D4单词测验，播放音标选择中英文阶段">
      <option name="closed" value="true" />
      <created>1748708634673</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1748708634673</updated>
    </task>
    <task id="LOCAL-00075" summary="feat：添加D4单词测验，播放音标选择中英文阶段">
      <option name="closed" value="true" />
      <created>1748709257634</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1748709257634</updated>
    </task>
    <task id="LOCAL-00076" summary="feat：当前单词没有学习完，不能学习下一个单词">
      <option name="closed" value="true" />
      <created>1748710604300</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1748710604300</updated>
    </task>
    <task id="LOCAL-00077" summary="feat：环节切换，点击下一步无需校验提交结果">
      <option name="closed" value="true" />
      <created>1748713738933</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1748713738933</updated>
    </task>
    <task id="LOCAL-00078" summary="feat：调整环节标题">
      <option name="closed" value="true" />
      <created>1748840339112</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1748840339112</updated>
    </task>
    <task id="LOCAL-00079" summary="feat：调整句子排序页面 &amp; 视频讲解页面">
      <option name="closed" value="true" />
      <created>1748872815103</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1748872815104</updated>
    </task>
    <task id="LOCAL-00080" summary="fix：切换环节的问题">
      <option name="closed" value="true" />
      <created>1748873613863</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1748873613863</updated>
    </task>
    <task id="LOCAL-00081" summary="feat：增加主页按钮，调整切换环节时，学生没作答 不显示正确答案">
      <option name="closed" value="true" />
      <created>1748875555239</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1748875555239</updated>
    </task>
    <task id="LOCAL-00082" summary="feat：将localstorage改为sessionStorage">
      <option name="closed" value="true" />
      <created>1748875874907</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1748875874907</updated>
    </task>
    <task id="LOCAL-00083" summary="feat：下课后，如果没有课前复习，给个提示">
      <option name="closed" value="true" />
      <created>1748879336796</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1748879336797</updated>
    </task>
    <task id="LOCAL-00084" summary="feat：下课后，如果没有课前复习，给个提示">
      <option name="closed" value="true" />
      <created>1748879675705</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1748879675705</updated>
    </task>
    <task id="LOCAL-00085" summary="feat：页面缩放自适应">
      <option name="closed" value="true" />
      <created>1748960523373</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1748960523374</updated>
    </task>
    <task id="LOCAL-00086" summary="feat：页面缩放自适应">
      <option name="closed" value="true" />
      <created>1748961680238</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1748961680238</updated>
    </task>
    <task id="LOCAL-00087" summary="feat：词汇测验">
      <option name="closed" value="true" />
      <created>1748964150837</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1748964150837</updated>
    </task>
    <task id="LOCAL-00088" summary="feat：词汇测验">
      <option name="closed" value="true" />
      <created>1748966630928</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1748966630928</updated>
    </task>
    <task id="LOCAL-00089" summary="feat：词汇测验">
      <option name="closed" value="true" />
      <created>1748966903126</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1748966903126</updated>
    </task>
    <task id="LOCAL-00090" summary="feat：添加完成学习的插槽">
      <option name="closed" value="true" />
      <created>1748967312312</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1748967312312</updated>
    </task>
    <task id="LOCAL-00091" summary="fix： 调整每个阶段页面的需要为动态传输">
      <option name="closed" value="true" />
      <created>1749212031382</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1749212031382</updated>
    </task>
    <task id="LOCAL-00092" summary="feat：添加倒计时数据逻辑">
      <option name="closed" value="true" />
      <created>1749218207729</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1749218207730</updated>
    </task>
    <task id="LOCAL-00093" summary="feat： 调整计时器样式">
      <option name="closed" value="true" />
      <created>1749218814379</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1749218814379</updated>
    </task>
    <task id="LOCAL-00094" summary="feat： 调整计时器">
      <option name="closed" value="true" />
      <created>1749221635291</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1749221635291</updated>
    </task>
    <task id="LOCAL-00095" summary="feat： 修改延长时间为正计时">
      <option name="closed" value="true" />
      <created>1749222638400</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1749222638403</updated>
    </task>
    <task id="LOCAL-00096" summary="feat： 修改延长时间为正计时">
      <option name="closed" value="true" />
      <created>1749222807269</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1749222807269</updated>
    </task>
    <task id="LOCAL-00097" summary="feat： 添加暂无视频弹窗提示">
      <option name="closed" value="true" />
      <created>1749308607459</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1749308607459</updated>
    </task>
    <task id="LOCAL-00098" summary="fix：150%缩放比自适应">
      <option name="closed" value="true" />
      <created>1749651527986</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1749651527988</updated>
    </task>
    <task id="LOCAL-00099" summary="fix：150%缩放比自适应">
      <option name="closed" value="true" />
      <created>1749662702355</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1749662702356</updated>
    </task>
    <task id="LOCAL-00100" summary="feat： 调整答案没选，提醒改为气泡窗">
      <option name="closed" value="true" />
      <created>1749823126143</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1749823126143</updated>
    </task>
    <task id="LOCAL-00101" summary="feat：微调">
      <option name="closed" value="true" />
      <created>1749823777333</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1749823777333</updated>
    </task>
    <task id="LOCAL-00102" summary="feat：倒计时结束后，结束本环节同时刷新课程信息">
      <option name="closed" value="true" />
      <created>1749823920223</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1749823920223</updated>
    </task>
    <task id="LOCAL-00103" summary="feat：复习课，上课后直接进入复习篮子抽屉；&#10;音频播放图标放大">
      <option name="closed" value="true" />
      <created>1749876040327</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1749876040327</updated>
    </task>
    <task id="LOCAL-00104" summary="feat：音视频提前缓存">
      <option name="closed" value="true" />
      <created>1749905268610</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1749905268610</updated>
    </task>
    <task id="LOCAL-00105" summary="feat：先屏蔽倒计时自动结束课程和环节">
      <option name="closed" value="true" />
      <created>1749909182704</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1749909182704</updated>
    </task>
    <task id="LOCAL-00106" summary="feat：关闭页面动效">
      <option name="closed" value="true" />
      <created>1749910463179</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1749910463179</updated>
    </task>
    <task id="LOCAL-00107" summary="feat：关闭页面动效">
      <option name="closed" value="true" />
      <created>1749911220651</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1749911220651</updated>
    </task>
    <task id="LOCAL-00108" summary="fix：修复几个拖拽相关需要检查答案的，会同时出现检查答案和未选择答案的重复提示">
      <option name="closed" value="true" />
      <created>1749921382874</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1749921382875</updated>
    </task>
    <task id="LOCAL-00109" summary="fix：修复句子翻译，选项选择正确没有赋值问题">
      <option name="closed" value="true" />
      <created>1750004023447</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1750004023448</updated>
    </task>
    <task id="LOCAL-00110" summary="fix：单词填空，答案检查正确自动播放音频">
      <option name="closed" value="true" />
      <created>1750005643362</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1750005643362</updated>
    </task>
    <task id="LOCAL-00111" summary="fix：句子排序，作答区改成三行显示">
      <option name="closed" value="true" />
      <created>1750079999517</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1750079999517</updated>
    </task>
    <task id="LOCAL-00112" summary="feat: 添加环节结束按钮">
      <option name="closed" value="true" />
      <created>1750080385190</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1750080385190</updated>
    </task>
    <task id="LOCAL-00113" summary="feat: 未添加环节，也显示下课按钮">
      <option name="closed" value="true" />
      <created>1750558914893</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1750558914893</updated>
    </task>
    <task id="LOCAL-00114" summary="feat: 单词测试页面判断">
      <option name="closed" value="true" />
      <created>1754320754605</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1754320754605</updated>
    </task>
    <task id="LOCAL-00115" summary="feat: 订单管理">
      <option name="closed" value="true" />
      <created>1754404007083</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1754404007083</updated>
    </task>
    <task id="LOCAL-00116" summary="feat: 订单管理">
      <option name="closed" value="true" />
      <created>1754408513441</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1754408513441</updated>
    </task>
    <option name="localTasksCounter" value="117" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/src/api/management/product.js" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/features/v1.0.0" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat：页面缩放自适应" />
    <MESSAGE value="feat：词汇测验" />
    <MESSAGE value="feat：添加完成学习的插槽" />
    <MESSAGE value="fix： 调整每个阶段页面的需要为动态传输" />
    <MESSAGE value="feat：添加倒计时数据逻辑" />
    <MESSAGE value="feat： 调整计时器样式" />
    <MESSAGE value="feat： 调整计时器" />
    <MESSAGE value="feat： 修改延长时间为正计时" />
    <MESSAGE value="feat： 添加暂无视频弹窗提示" />
    <MESSAGE value="fix：150%缩放比自适应" />
    <MESSAGE value="feat： 调整答案没选，提醒改为气泡窗" />
    <MESSAGE value="feat：微调" />
    <MESSAGE value="feat：倒计时结束后，结束本环节同时刷新课程信息" />
    <MESSAGE value="feat：复习课，上课后直接进入复习篮子抽屉；&#10;音频播放图标放大" />
    <MESSAGE value="feat：音视频提前缓存" />
    <MESSAGE value="feat：先屏蔽倒计时自动结束课程和环节" />
    <MESSAGE value="feat：关闭页面动效" />
    <MESSAGE value="fix：修复几个拖拽相关需要检查答案的，会同时出现检查答案和未选择答案的重复提示" />
    <MESSAGE value="fix：修复句子翻译，选项选择正确没有赋值问题" />
    <MESSAGE value="fix：单词填空，答案检查正确自动播放音频" />
    <MESSAGE value="fix：句子排序，作答区改成三行显示" />
    <MESSAGE value="feat: 添加环节结束按钮" />
    <MESSAGE value="feat: 未添加环节，也显示下课按钮" />
    <MESSAGE value="feat: 单词测试页面判断" />
    <MESSAGE value="feat: 订单管理" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 订单管理" />
  </component>
</project>