-- Flyway migration script for PostgreSQL 17.0
-- 创建产品表（课时包）
-- Version: V1
-- Description: Create product table
-- Date: 2025-08-05

-- 创建产品表
CREATE TABLE IF NOT EXISTS product (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- 课时属性
    type VARCHAR(50) NOT NULL,
    subject VARCHAR(50) NOT NULL,                    -- 学科
    course_type VARCHAR(100),                        -- 课型
    applicable_grades JSONB,                         -- 适用年级（多选，JSON数组）
    unit_price BIGINT NOT NULL,                      -- 单价（分）
    quantity INTEGER NOT NULL,                       -- 数量
    
    -- 赠送课时
    has_bonus_hours BOOLEAN DEFAULT FALSE,           -- 是否有赠送课时
    bonus_hours_quantity INTEGER DEFAULT 0,          -- 赠送课时数量
    
    -- 教材费
    has_material_fee BOOLEAN DEFAULT FALSE,          -- 是否包含教材费
    material_fee BIGINT DEFAULT 0,                   -- 教材费（分）
    
    -- 价格计算
    original_price BIGINT NOT NULL,                  -- 原价（分）= 单价 * 数量 + 教材费
    selling_price BIGINT NOT NULL,                   -- 售价（分）
    
    -- 产品状态和管理
    status VARCHAR(20) NOT NULL DEFAULT '上架',      -- 产品状态（上架、下架）
    cover_image VARCHAR(500),                        -- 产品封面图片URL
    detail_images JSONB,                             -- 产品详情图片URLs（JSON数组）
    sort_order INTEGER DEFAULT 0,                    -- 排序权重
    remark TEXT,                                     -- 备注
    
    -- 库存管理
    stock INTEGER DEFAULT 0,                         -- 库存数量
    stock_limited BOOLEAN DEFAULT FALSE,             -- 是否限制库存
    sales_count INTEGER DEFAULT 0,                   -- 销售数量
    
    -- 标签和内容
    tags JSONB,                                      -- 产品标签（JSON数组）
    content TEXT,                                    -- 产品详情内容
    
    -- 审计字段
    create_by VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 创建索引
CREATE INDEX idx_product_subject ON product(subject);
CREATE INDEX idx_product_course_type ON product(course_type);
CREATE INDEX idx_product_status ON product(status);
CREATE INDEX idx_product_sort_order ON product(sort_order);
CREATE INDEX idx_product_sales_count ON product(sales_count);
CREATE INDEX idx_product_create_time ON product(create_time);
CREATE INDEX idx_product_deleted ON product(deleted);

-- 创建GIN索引用于JSON字段查询
CREATE INDEX idx_product_applicable_grades ON product USING GIN(applicable_grades);
CREATE INDEX idx_product_tags ON product USING GIN(tags);

-- 添加表注释
COMMENT ON TABLE product IS '产品信息表（课时包）';
COMMENT ON COLUMN product.id IS '产品ID';
COMMENT ON COLUMN product.name IS '产品名称';
COMMENT ON COLUMN product.description IS '产品描述';
COMMENT ON COLUMN product.subject IS '学科';
COMMENT ON COLUMN product.course_type IS '课型';
COMMENT ON COLUMN product.applicable_grades IS '适用年级（JSON数组）';
COMMENT ON COLUMN product.unit_price IS '单价（分）';
COMMENT ON COLUMN product.quantity IS '数量';
COMMENT ON COLUMN product.has_bonus_hours IS '是否有赠送课时';
COMMENT ON COLUMN product.bonus_hours_quantity IS '赠送课时数量';
COMMENT ON COLUMN product.has_material_fee IS '是否包含教材费';
COMMENT ON COLUMN product.material_fee IS '教材费（分）';
COMMENT ON COLUMN product.original_price IS '原价（分）';
COMMENT ON COLUMN product.selling_price IS '售价（分）';
COMMENT ON COLUMN product.status IS '产品状态';
COMMENT ON COLUMN product.cover_image IS '产品封面图片URL';
COMMENT ON COLUMN product.detail_images IS '产品详情图片URLs（JSON数组）';
COMMENT ON COLUMN product.sort_order IS '排序权重';
COMMENT ON COLUMN product.remark IS '备注';
COMMENT ON COLUMN product.stock IS '库存数量';
COMMENT ON COLUMN product.stock_limited IS '是否限制库存';
COMMENT ON COLUMN product.sales_count IS '销售数量';
COMMENT ON COLUMN product.tags IS '产品标签（JSON数组）';
COMMENT ON COLUMN product.content IS '产品详情内容';
COMMENT ON COLUMN product.create_by IS '创建者';
COMMENT ON COLUMN product.create_time IS '创建时间';
COMMENT ON COLUMN product.update_by IS '更新者';
COMMENT ON COLUMN product.update_time IS '更新时间';
COMMENT ON COLUMN product.deleted IS '删除标志';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_product_updated_time 
    BEFORE UPDATE ON product 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_time_column();
