<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TextbookMapper">

    <select id="getTextbookTree" resultType="org.nonamespace.word.server.dto.TextbookTreeItemDto"
            parameterType="org.nonamespace.word.server.dto.TextbookItemTreeQueryDto">
        SELECT
        ti.id as nodeId,
        ti.unit_name as unitName,
        ti.pid as pid,
        case when ti.node_type = 2 then ti.textbook_id when ti.node_type=3 then ti.pid else '' end as parentNodeId,
        ti.node_type as nodeType,
        ti.textbook_id as textbookId,
        ti.word_id as wordId,
        t."name" as textbookName,
        w.word,
        t."type" as textbookType,
        ti.display_order as displayOrder,
        ti.create_time as createTime
        FROM words.textbook_item ti
        left join words.word w on w.id = ti.word_id
        left join words.textbook t on ti.textbook_id = t.id
        where (w.deleted = false or ti.deleted = false or t.deleted = false)
        <if test="types != null and types.size > 0">
            and t.type in
            <foreach collection="types" item="textbookType" open="(" separator="," close=")">
                #{textbookType}
            </foreach>
        </if>

        <if test="nodeType != null and nodeType == 1">
            and ti.textbook_id = #{nodeId} and ti.pid='0'
        </if>

        <if test="nodeType != null and nodeType == 2">
            and ti.pid = #{nodeId}
        </if>

<!--        每点击树节点，则仅查询章节-->
        <if test="nodeType != null and nodeType == -1 ">
            and ti.pid = '0'
        </if>
        order by ti.textbook_id,ti.node_type,ti.display_order, t.create_time
    </select>
</mapper>