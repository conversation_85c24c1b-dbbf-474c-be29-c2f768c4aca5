<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.StudentTextbookProgressMapper">
    
    <resultMap type="StudentTextbookProgress" id="StudentTextbookProgressResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="statWordCnt"    column="stat_word_cnt"    />
        <result property="statWordLearnCnt"    column="stat_word_learn_cnt"    />
        <result property="statWordMistakesCnt"    column="stat_word_mistakes_cnt"    />
        <result property="firstLearnAt"    column="first_learn_at"    />
        <result property="lastLearnAt"    column="last_learn_at"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="deleted"    column="deleted"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
    </resultMap>

    <sql id="selectStudentTextbookProgressVo">
        select id, textbook_id, student_id, stat_word_cnt, stat_word_learn_cnt, stat_word_mistakes_cnt, first_learn_at, last_learn_at, status, version, deleted, created_at, updated_at, created_by, updated_by from student_textbook_progress
    </sql>

    <select id="selectStudentTextbookProgressList" parameterType="StudentTextbookProgress" resultMap="StudentTextbookProgressResult">
        <include refid="selectStudentTextbookProgressVo"/>
        <where>  
            <if test="textbookId != null  and textbookId != ''"> and textbook_id = #{textbookId}</if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="statWordCnt != null "> and stat_word_cnt = #{statWordCnt}</if>
            <if test="statWordLearnCnt != null "> and stat_word_learn_cnt = #{statWordLearnCnt}</if>
            <if test="statWordMistakesCnt != null "> and stat_word_mistakes_cnt = #{statWordMistakesCnt}</if>
            <if test="firstLearnAt != null  and firstLearnAt != ''"> and first_learn_at = #{firstLearnAt}</if>
            <if test="lastLearnAt != null  and lastLearnAt != ''"> and last_learn_at = #{lastLearnAt}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="deleted != null  and deleted != ''"> and deleted = #{deleted}</if>
            <if test="createdAt != null  and createdAt != ''"> and created_at = #{createdAt}</if>
            <if test="updatedAt != null  and updatedAt != ''"> and updated_at = #{updatedAt}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectStudentTextbookProgressById" parameterType="String" resultMap="StudentTextbookProgressResult">
    </select>

    <insert id="insertStudentTextbookProgress" parameterType="StudentTextbookProgress">
        insert into student_textbook_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null and textbookId != ''">textbook_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="statWordCnt != null">stat_word_cnt,</if>
            <if test="statWordLearnCnt != null">stat_word_learn_cnt,</if>
            <if test="statWordMistakesCnt != null">stat_word_mistakes_cnt,</if>
            <if test="firstLearnAt != null">first_learn_at,</if>
            <if test="lastLearnAt != null">last_learn_at,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version,</if>
            <if test="deleted != null">deleted,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null and textbookId != ''">#{textbookId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="statWordCnt != null">#{statWordCnt},</if>
            <if test="statWordLearnCnt != null">#{statWordLearnCnt},</if>
            <if test="statWordMistakesCnt != null">#{statWordMistakesCnt},</if>
            <if test="firstLearnAt != null">#{firstLearnAt},</if>
            <if test="lastLearnAt != null">#{lastLearnAt},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
         </trim>
    </insert>

    <update id="updateStudentTextbookProgress" parameterType="StudentTextbookProgress">
        update student_textbook_progress
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null and textbookId != ''">textbook_id = #{textbookId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="statWordCnt != null">stat_word_cnt = #{statWordCnt},</if>
            <if test="statWordLearnCnt != null">stat_word_learn_cnt = #{statWordLearnCnt},</if>
            <if test="statWordMistakesCnt != null">stat_word_mistakes_cnt = #{statWordMistakesCnt},</if>
            <if test="firstLearnAt != null">first_learn_at = #{firstLearnAt},</if>
            <if test="lastLearnAt != null">last_learn_at = #{lastLearnAt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudentTextbookProgressById" parameterType="String">
        delete from student_textbook_progress where id = #{id}
    </delete>

    <delete id="deleteStudentTextbookProgressByIds" parameterType="String">
        delete from student_textbook_progress where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>