<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TeacherStudentRelationMapper">

    <!-- 根据教师ID查询学生ID列表 -->
    <select id="selectStudentIdsByTeacher" resultType="java.lang.String">
        SELECT DISTINCT student_id
        FROM teacher_student_relation
        WHERE teacher_id = #{teacherId}
        AND status = 'active'
        AND deleted = false
    </select>

    <!-- 根据学生ID查询教师ID -->
    <select id="selectTeacherIdByStudent" resultType="java.lang.String">
        SELECT teacher_id
        FROM teacher_student_relation
        WHERE student_id = #{studentId}
        AND status = 'active'
        AND deleted = false
        LIMIT 1
    </select>

    <!-- 分配学生给教师 -->
    <insert id="assignStudentsToTeacher">
        INSERT INTO teacher_student_relation (
            id, teacher_id, student_id, relation_type, status, 
            start_date, create_time, update_time, create_by, deleted
        ) VALUES
        <foreach collection="studentIds" item="studentId" separator=",">
            (
                CONCAT('tsr_', #{teacherId}, '_', #{studentId}, '_', EXTRACT(EPOCH FROM NOW())),
                #{teacherId},
                #{studentId},
                'teaching',
                'active',
                CURRENT_DATE,
                NOW(),
                NOW(),
                'system',
                false
            )
        </foreach>
        ON CONFLICT (teacher_id, student_id, deleted) 
        WHERE deleted = false
        DO UPDATE SET
            status = 'active',
            start_date = CURRENT_DATE,
            update_time = NOW()
    </insert>

    <!-- 取消师生关系 -->
    <update id="removeTeacherStudentRelation">
        UPDATE teacher_student_relation SET
            status = 'inactive',
            end_date = CURRENT_DATE,
            deleted = true,
            update_time = NOW()
        WHERE teacher_id = #{teacherId}
        AND student_id = #{studentId}
        AND deleted = false
    </update>

    <!-- 取消学生的所有教师关系 -->
    <update id="removeAllTeachersByStudent">
        UPDATE teacher_student_relation SET
            status = 'inactive',
            end_date = CURRENT_DATE,
            update_time = NOW(),
            deleted = true
        WHERE student_id = #{studentId}
        AND status = 'active'
        AND deleted = false
    </update>

    <!-- 取消教师的所有学生关系 -->
    <update id="removeAllStudentsByTeacher">
        UPDATE teacher_student_relation SET
            status = 'inactive',
            deteted = true,
            end_date = CURRENT_DATE,
            update_time = NOW()
        WHERE teacher_id = #{teacherId}
        AND status = 'active'
        AND deleted = false
    </update>

    <!-- 检查师生关系是否存在 -->
    <select id="existsTeacherStudentRelation" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM teacher_student_relation
        WHERE teacher_id = #{teacherId}
        AND student_id = #{studentId}
        AND status = 'active'
        AND deleted = false
    </select>

</mapper>
