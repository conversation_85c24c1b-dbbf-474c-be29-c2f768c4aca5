21:46:00.656 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
21:46:00.712 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 10456 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
21:46:00.713 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
21:46:07.671 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
21:46:08.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
21:46:08.831 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
21:46:08.832 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
21:46:08.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
21:46:09.955 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
21:46:11.934 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
21:46:11.983 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
21:46:12.079 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
21:46:12.135 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
21:46:13.306 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
21:46:13.557 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
21:46:13.605 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
21:46:20.388 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
21:46:20.391 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
21:46:21.856 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
21:46:21.872 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
21:46:21.873 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
21:46:21.874 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
21:46:21.874 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

21:46:21.874 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
21:46:21.874 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
21:46:21.874 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@785d194d
21:46:23.724 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
21:46:23.736 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
21:46:23.749 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 23.537 seconds (process running for 26.564)
21:46:24.929 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:46:28.802 [schedule-pool-1] INFO  sys-user - [run,55] - [System|系统] - [127.0.0.1]内网IP[15005014480][Success][登录成功]
21:46:37.599 [http-nio-8080-exec-9] INFO  o.n.w.s.s.i.CourseServiceImpl - [preflightCourse,813] - [3850|筱灵老师] - 上课预检, courseId: 1942413376603701252
21:46:42.197 [http-nio-8080-exec-10] INFO  o.n.w.s.s.i.CourseServiceImpl - [preflightCourse,813] - [3850|筱灵老师] - 上课预检, courseId: 1952332155724603392
21:46:44.853 [http-nio-8080-exec-12] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:46:57.246 [http-nio-8080-exec-14] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621016350721
21:46:57.982 [http-nio-8080-exec-14] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1224] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952365644354084864, reviewId: 1936114621016350721
21:46:58.064 [http-nio-8080-exec-13] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:47:06.605 [http-nio-8080-exec-16] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:47:10.699 [http-nio-8080-exec-18] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:47:18.251 [http-nio-8080-exec-20] INFO  o.n.w.s.s.i.TextbookService - [treeSearch,289] - [3850|筱灵老师] - 查询教材树耗时:234，查询最后学习单词耗时： 0
21:47:23.614 [http-nio-8080-exec-25] INFO  o.n.w.s.s.i.CourseServiceImpl - [startLearning,1059] - [3850|筱灵老师] - 开始新课程学习, courseId: 1952332155724603392, textbookId: 1937751018810847233, unitId: 1937751018811908096, wordIds: [1925028510401871874, 1925028509038723077], textbookItemIds: [1937751018811908097, 1937751018811908098]
21:47:24.115 [http-nio-8080-exec-25] INFO  o.n.w.s.s.i.CourseServiceImpl - [startLearning,1084] - [3850|筱灵老师] - 成功开始新课程学习, courseId: 1952332155724603392, sectionId: 1952365754198712320
21:47:24.235 [http-nio-8080-exec-26] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:47:47.801 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:47:52.399 [http-nio-8080-exec-28] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621016350721
21:47:52.520 [http-nio-8080-exec-28] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952365644354084864
21:47:52.819 [http-nio-8080-exec-28] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952365875346989056, reviewId: 1936114621016350721
21:47:52.916 [http-nio-8080-exec-29] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:48:01.724 [http-nio-8080-exec-33] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:48:05.043 [http-nio-8080-exec-35] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621016350721
21:48:05.152 [http-nio-8080-exec-35] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952365875346989056
21:48:05.460 [http-nio-8080-exec-35] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952365928346214400, reviewId: 1936114621016350721
21:48:05.537 [http-nio-8080-exec-36] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:48:27.030 [http-nio-8080-exec-38] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:49:21.075 [http-nio-8080-exec-44] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621016350721
21:49:21.199 [http-nio-8080-exec-44] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952365928346214400
21:49:21.509 [http-nio-8080-exec-44] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952366247306256384, reviewId: 1936114621016350721
21:49:21.604 [http-nio-8080-exec-42] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:49:25.237 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1815] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952366247310450690, result: 错误
21:49:25.395 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1850] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952366247310450690, result: 错误
21:49:27.477 [http-nio-8080-exec-45] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1815] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952366247306256385, result: 正确
21:49:27.632 [http-nio-8080-exec-45] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1850] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952366247306256385, result: 正确
21:49:47.635 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1815] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952366247310450691, result: 错误
21:49:47.792 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1850] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952366247310450691, result: 错误
21:49:57.815 [http-nio-8080-exec-48] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:50:11.028 [http-nio-8080-exec-50] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621012156418
21:50:11.156 [http-nio-8080-exec-50] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952353484020039680
21:50:11.430 [http-nio-8080-exec-50] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952366456824324096, reviewId: 1936114621012156418
21:50:11.501 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:51:10.333 [http-nio-8080-exec-53] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:51:12.473 [http-nio-8080-exec-54] INFO  o.n.w.s.s.i.TextbookService - [treeSearch,289] - [3850|筱灵老师] - 查询教材树耗时:210，查询最后学习单词耗时： 0
21:51:16.578 [http-nio-8080-exec-60] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1935675241046974466
21:51:16.709 [http-nio-8080-exec-60] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952354554460946432
21:51:17.210 [http-nio-8080-exec-60] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952366731878391808, reviewId: 1935675241046974466
21:51:17.293 [http-nio-8080-exec-58] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:54:08.672 [http-nio-8080-exec-57] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
21:54:33.762 [http-nio-8080-exec-69] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1948985438026969089
21:54:33.893 [http-nio-8080-exec-69] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1128] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952366524723544064
21:54:34.224 [http-nio-8080-exec-69] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1196] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952367558928031744, reviewId: 1948985438026969089
21:54:34.303 [http-nio-8080-exec-64] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:12:10.185 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/47.107.84.96:19736
22:12:10.325 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
22:12:47.038 [http-nio-8080-exec-74] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:25:58.633 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:25:58.672 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 31420 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:25:58.673 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:26:00.770 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:26:01.667 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:26:01.669 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:26:01.669 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:26:01.736 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:26:02.290 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:26:03.627 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:26:03.670 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:26:03.744 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:26:03.785 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:26:04.675 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:26:04.819 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:26:04.852 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:26:10.226 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
22:26:10.229 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
22:26:11.498 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:26:11.512 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:26:11.512 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:26:11.512 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:26:11.513 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:26:11.513 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:26:11.513 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:26:11.513 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bf56981
22:26:13.194 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:26:13.206 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:26:13.220 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 14.961 seconds (process running for 16.13)
22:26:13.729 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:26:14.261 [http-nio-8080-exec-2] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:26:22.476 [http-nio-8080-exec-3] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:26:49.060 [http-nio-8080-exec-4] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:26:52.358 [http-nio-8080-exec-5] INFO  o.n.w.s.s.i.CourseServiceImpl - [startEndReview,1230] - [3850|筱灵老师] - 开始下课前复习, courseId: 1952332155724603392
22:26:52.536 [http-nio-8080-exec-5] INFO  o.n.w.s.s.i.CourseServiceImpl - [startEndReview,1250] - [3850|筱灵老师] - 本次课程没有错误单词，无需下课前复习
22:26:59.173 [http-nio-8080-exec-7] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936967197639278594
22:26:59.863 [http-nio-8080-exec-7] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1224] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952375718678020096, reviewId: 1936967197639278594
22:26:59.946 [http-nio-8080-exec-8] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:27:01.924 [http-nio-8080-exec-13] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:27:10.262 [http-nio-8080-exec-12] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1935675241051168770
22:27:11.004 [http-nio-8080-exec-12] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1224] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952375765184462848, reviewId: 1935675241051168770
22:27:11.081 [http-nio-8080-exec-17] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:27:24.242 [http-nio-8080-exec-11] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1815] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952375765830385664, result: 正确
22:27:24.424 [http-nio-8080-exec-11] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1850] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952375765830385664, result: 正确
22:27:24.939 [http-nio-8080-exec-15] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1815] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952375765830385665, result: 正确
22:27:25.114 [http-nio-8080-exec-15] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1850] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952375765830385665, result: 正确
22:27:27.924 [http-nio-8080-exec-10] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:27:39.589 [http-nio-8080-exec-19] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1091] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1949056722052173827
22:27:40.123 [http-nio-8080-exec-19] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1224] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952375887800745984, reviewId: 1949056722052173827
22:27:40.197 [http-nio-8080-exec-20] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:45:13.169 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:45:13.442 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
22:45:13.443 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:45:13.445 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
22:45:13.446 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
22:45:13.519 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:45:13.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:45:13.622 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:45:19.076 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:45:19.182 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 25784 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:45:19.184 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:45:21.452 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:45:22.408 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:45:22.410 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:45:22.410 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:45:22.494 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:45:23.058 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:45:24.521 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:45:24.559 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:45:24.649 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:45:24.693 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:45:25.788 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:45:25.965 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:45:26.021 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:45:33.252 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,26] - [System|系统] - 线程池初始化中...
22:45:33.255 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,29] - [System|系统] - 线程池初始化完成...
22:45:34.731 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:45:34.747 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:45:34.747 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:45:34.748 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:45:34.749 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:45:34.749 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:45:34.749 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:45:34.749 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@786384a6
22:45:36.403 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:45:36.414 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:45:36.425 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 18.016 seconds (process running for 19.509)
22:45:40.484 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:45:41.077 [http-nio-8080-exec-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:45:56.203 [http-nio-8080-exec-2] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:48:49.128 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:48:49.192 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 17352 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:48:49.192 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:48:53.893 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:48:54.756 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:48:54.758 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:48:54.758 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:48:54.822 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:48:55.401 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:48:56.976 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:48:57.077 [main] INFO  o.f.c.FlywayExecutor - [info,37] - [System|系统] - Database: ************************************************************************** (PostgreSQL 17.4)
22:48:57.454 [main] INFO  o.f.c.i.c.DbValidate - [info,37] - [System|系统] - Successfully validated 4 migrations (execution time 00:00.177s)
22:48:57.705 [main] INFO  o.f.c.i.c.DbMigrate - [info,37] - [System|系统] - Current version of schema "words": 1.1.0.001
22:48:57.730 [main] INFO  o.f.c.i.c.DbMigrate - [info,37] - [System|系统] - Schema "words" is up to date. No migration necessary.
22:48:57.973 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:48:58.065 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:48:58.120 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:48:59.148 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:48:59.289 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:48:59.321 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:49:05.732 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,30] - [System|系统] - 线程池初始化中...
22:49:05.734 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,33] - [System|系统] - 线程池初始化完成...
22:49:06.844 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:49:06.864 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:49:06.864 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:49:06.865 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:49:06.866 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:49:06.866 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:49:06.866 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:49:06.867 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7a2b0101
22:49:08.331 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:49:08.341 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:49:08.350 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 19.629 seconds (process running for 21.229)
22:49:50.561 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:49:51.057 [http-nio-8080-exec-2] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:50:06.605 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:50:06.734 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
22:50:06.735 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
22:50:06.735 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
22:50:06.735 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - [System|系统] - ====关闭后台任务任务线程池====
22:50:06.746 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:50:06.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:50:06.759 [SpringApplicationShutdownHook] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:50:09.796 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:50:09.839 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 20096 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:50:09.840 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:50:11.588 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:50:12.360 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:50:12.361 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:50:12.361 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:50:12.425 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:50:12.934 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:50:14.386 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:50:14.453 [main] INFO  o.f.c.FlywayExecutor - [info,37] - [System|系统] - Database: ********************************************************************** (PostgreSQL 17.4)
22:50:14.832 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:50:14.835 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:50:14.850 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
22:50:14.851 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:50:28.461 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:50:28.510 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 16140 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:50:28.511 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:50:30.237 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:50:31.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:50:31.018 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:50:31.018 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:50:31.082 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:50:31.572 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:50:33.456 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:50:33.561 [main] INFO  o.f.c.FlywayExecutor - [info,37] - [System|系统] - Database: ********************************************************************** (PostgreSQL 17.4)
22:50:34.041 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - [System|系统] - {dataSource-1} closing ...
22:50:34.044 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - [System|系统] - {dataSource-1} closed
22:50:34.060 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Stopping service [Tomcat]
22:50:34.062 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [destroy,58] - [System|系统] - LogMDCFilter 销毁完成
22:51:01.245 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - [System|系统] - HV000001: Hibernate Validator 8.0.1.Final
22:51:01.294 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarting,50] - [System|系统] - Starting WssLauncherApplication using Java 21.0.6 with PID 13416 (E:\idea_projects\word-system-service\wss-launcher\target\classes started by 79304 in E:\idea_projects\word-system-service)
22:51:01.295 [main] INFO  o.n.w.l.WssLauncherApplication - [logStartupProfileInfo,660] - [System|系统] - The following 1 profile is active: "dev"
22:51:03.109 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
22:51:03.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Initializing ProtocolHandler ["http-nio-8080"]
22:51:03.870 [main] INFO  o.a.c.c.StandardService - [log,173] - [System|系统] - Starting service [Tomcat]
22:51:03.871 [main] INFO  o.a.c.c.StandardEngine - [log,173] - [System|系统] - Starting Servlet engine: [Apache Tomcat/10.1.24]
22:51:03.935 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring embedded WebApplicationContext
22:51:04.423 [main] INFO  o.n.w.c.w.f.LogMDCFilter - [init,26] - [System|系统] - LogMDCFilter 初始化完成
22:51:05.718 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - [System|系统] - {dataSource-1} inited
22:51:05.749 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - [System|系统] - HHH000204: Processing PersistenceUnitInfo [name: default]
22:51:05.817 [main] INFO  o.hibernate.Version - [logVersion,44] - [System|系统] - HHH000412: Hibernate ORM core version 6.5.2.Final
22:51:05.850 [main] INFO  o.h.c.i.RegionFactoryInitiator - [initiateService,50] - [System|系统] - HHH000026: Second-level cache disabled
22:51:06.607 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,59] - [System|系统] - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
22:51:06.744 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join properties config complete
22:51:06.773 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - [System|系统] - mybatis plus join SqlInjector init
22:51:11.884 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,30] - [System|系统] - 线程池初始化中...
22:51:11.885 [main] INFO  o.n.w.c.u.ThreadPoolService - [init,33] - [System|系统] - 线程池初始化完成...
22:51:12.870 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - [System|系统] - Using default implementation for ThreadExecutor
22:51:12.880 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - [System|系统] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:51:12.880 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - [System|系统] - Quartz Scheduler v.2.3.2 created.
22:51:12.882 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - [System|系统] - RAMJobStore initialized.
22:51:12.882 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - [System|系统] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:51:12.883 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - [System|系统] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:51:12.883 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - [System|系统] - Quartz scheduler version: 2.3.2
22:51:12.883 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - [System|系统] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@f4352c6
22:51:14.221 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - [System|系统] - Starting ProtocolHandler ["http-nio-8080"]
22:51:14.229 [main] INFO  o.q.c.QuartzScheduler - [start,547] - [System|系统] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:51:14.238 [main] INFO  o.n.w.l.WssLauncherApplication - [logStarted,56] - [System|系统] - Started WssLauncherApplication in 13.43 seconds (process running for 14.433)
22:51:51.461 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - [System|系统] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:51:51.921 [http-nio-8080-exec-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:51:59.300 [http-nio-8080-exec-2] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:52:07.850 [http-nio-8080-exec-3] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:55:55.297 [http-nio-8080-exec-5] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:57:50.501 [http-nio-8080-exec-4] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:57:52.131 [http-nio-8080-exec-7] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:57:53.753 [http-nio-8080-exec-8] INFO  o.n.w.s.s.i.TextbookService - [treeSearch,289] - [3850|筱灵老师] - 查询教材树耗时:272，查询最后学习单词耗时： 0
22:57:59.055 [http-nio-8080-exec-13] INFO  o.n.w.s.s.i.CourseServiceImpl - [startLearning,1065] - [3850|筱灵老师] - 开始新课程学习, courseId: 1952332155724603392, textbookId: 1937753841791348738, unitId: 1937753841796603904, wordIds: [1925028515636363274, 1925028512012484616], textbookItemIds: [1937753841796603905, 1937753841796603906]
22:57:59.766 [http-nio-8080-exec-13] INFO  o.n.w.s.s.i.CourseServiceImpl - [startLearning,1090] - [3850|筱灵老师] - 成功开始新课程学习, courseId: 1952332155724603392, sectionId: 1952383519236222976
22:57:59.942 [http-nio-8080-exec-14] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:58:09.429 [http-nio-8080-exec-15] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060352, result: 正确
22:58:09.629 [http-nio-8080-exec-15] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383519945060352, result: 正确
22:58:13.636 [http-nio-8080-exec-16] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060352, result: 错误
22:58:13.860 [http-nio-8080-exec-19] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060353, result: 正确
22:58:14.050 [http-nio-8080-exec-19] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383519945060353, result: 正确
22:58:15.398 [http-nio-8080-exec-18] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060354, result: 正确
22:58:15.589 [http-nio-8080-exec-18] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383519945060354, result: 正确
22:58:15.955 [http-nio-8080-exec-17] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060355, result: 正确
22:58:16.124 [http-nio-8080-exec-17] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383519945060355, result: 正确
22:58:19.097 [http-nio-8080-exec-23] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383519945060356, result: 正确
22:58:19.434 [http-nio-8080-exec-23] INFO  o.n.w.s.s.i.CourseServiceImpl - [updateStudentLearnProgress,1002] - [3850|筱灵老师] - 更新学生学习进度, courseId: 1952332155724603392, textbookItemId: 1937753841796603905, isCorrect: true
22:58:19.671 [http-nio-8080-exec-23] INFO  o.n.w.s.s.i.CourseServiceImpl - [updateStudentLearnProgress,1060] - [3850|筱灵老师] - 学生学习进度更新成功, courseId: 1952332155724603392, textbookItemId: 1937753841796603905, isCorrect: true
22:58:19.673 [http-nio-8080-exec-23] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383519945060356, result: 正确
22:58:20.515 [http-nio-8080-exec-24] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383520083472384, result: 错误
22:58:20.766 [http-nio-8080-exec-24] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383520083472384, result: 错误
22:58:56.648 [http-nio-8080-exec-25] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383520083472385, result: 正确
22:58:56.908 [http-nio-8080-exec-25] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383520083472385, result: 正确
22:58:59.705 [http-nio-8080-exec-20] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383520083472386, result: 正确
22:58:59.954 [http-nio-8080-exec-20] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383520083472386, result: 正确
22:59:00.253 [http-nio-8080-exec-21] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383520083472387, result: 正确
22:59:00.493 [http-nio-8080-exec-21] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383520083472387, result: 正确
22:59:03.101 [http-nio-8080-exec-26] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
22:59:03.124 [http-nio-8080-exec-22] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952383520083472388, result: 错误
22:59:03.444 [http-nio-8080-exec-22] INFO  o.n.w.s.s.i.CourseServiceImpl - [updateStudentLearnProgress,1002] - [3850|筱灵老师] - 更新学生学习进度, courseId: 1952332155724603392, textbookItemId: 1937753841796603906, isCorrect: false
22:59:03.597 [http-nio-8080-exec-22] INFO  o.n.w.s.s.i.CourseServiceImpl - [updateStudentLearnProgress,1060] - [3850|筱灵老师] - 学生学习进度更新成功, courseId: 1952332155724603392, textbookItemId: 1937753841796603906, isCorrect: false
22:59:03.598 [http-nio-8080-exec-22] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952383520083472388, result: 错误
22:59:07.522 [http-nio-8080-exec-27] INFO  o.n.w.s.s.i.TextbookService - [treeSearch,289] - [3850|筱灵老师] - 查询教材树耗时:195，查询最后学习单词耗时： 0
23:00:02.975 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1097] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1936114621012156418
23:00:03.228 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1134] - [3850|筱灵老师] - 复习计划已在进行中，复制上次复习进度数据: sectionId: 1952366456824324096
23:00:03.466 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseSectionDto,1367] - [3850|筱灵老师] - 构建复习进度映射: textbookItemIds=2, wordInfos数量=2
23:00:03.467 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseSectionDto,1387] - [3850|筱灵老师] - 映射完成: 成功映射2个单词
23:00:03.474 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseSectionDto,1428] - [3850|筱灵老师] - 设置当前步骤索引: textbookItemId=1935253243038928897, stepIndex=2
23:00:03.476 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseSectionDto,1428] - [3850|筱灵老师] - 设置当前步骤索引: textbookItemId=1935253243038928898, stepIndex=0
23:00:03.702 [http-nio-8080-exec-30] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1202] - [3850|筱灵老师] - 成功恢复抗遗忘复习进度, courseId: 1952332155724603392, newSectionId: 1952384039665463296, reviewId: 1936114621012156418
23:00:03.794 [http-nio-8080-exec-31] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:00:08.877 [http-nio-8080-exec-32] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:00:46.249 [http-nio-8080-exec-33] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:01:45.160 [http-nio-8080-exec-35] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:01:56.966 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1097] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1950203536201560065
23:01:57.511 [http-nio-8080-exec-43] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1230] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952384517069533184, reviewId: 1950203536201560065
23:01:57.591 [http-nio-8080-exec-38] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:02:00.847 [http-nio-8080-exec-39] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:02:02.346 [http-nio-8080-exec-46] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:02:20.141 [http-nio-8080-exec-37] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1097] - [3850|筱灵老师] - 开始抗遗忘复习, courseId: 1952332155724603392, reviewId: 1950203536188977154
23:02:20.701 [http-nio-8080-exec-37] INFO  o.n.w.s.s.i.CourseServiceImpl - [startReview,1230] - [3850|筱灵老师] - 成功开始抗遗忘复习, courseId: 1952332155724603392, sectionId: 1952384614259945472, reviewId: 1950203536188977154
23:02:20.797 [http-nio-8080-exec-44] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:02:23.872 [http-nio-8080-exec-50] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952384614855536640, result: 错误
23:02:24.118 [http-nio-8080-exec-50] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952384614855536640, result: 错误
23:05:19.672 [http-nio-8080-exec-52] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1847] - [3850|筱灵老师] - 提交课程学习步骤结果, stepId: 1952384614855536641, result: 正确
23:05:19.910 [http-nio-8080-exec-52] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseStepSubmit,1882] - [3850|筱灵老师] - 成功提交课程学习步骤结果, stepId: 1952384614855536641, result: 正确
23:05:22.293 [http-nio-8080-exec-55] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:09:46.925 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - [System|系统] - Reconnecting, last destination was word.121tongbu.com/47.107.84.96:19736
23:09:47.041 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - [System|系统] - Reconnected to word.121tongbu.com/<unresolved>:19736
23:09:47.128 [http-nio-8080-exec-45] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:09:47.128 [http-nio-8080-exec-41] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:09:47.291 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [endCourse,1546] - [3850|筱灵老师] - 结束课程, courseId: 1952332155724603392
23:09:47.467 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [performCourseHoursConsumption,1689] - [3850|筱灵老师] - 开始执行课消逻辑: courseId=1952332155724603392, studentId=3851, subject=英语, specification=单词课, type=学习课, duration=60
23:09:47.468 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [performCourseHoursConsumption,1716] - [3850|筱灵老师] - 计算课消课时数: courseId=1952332155724603392, duration=60分钟, consumedHours=1.00课时
23:09:47.573 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.StudentCourseConsumptionServiceImpl - [recordConsumption,57] - [3850|筱灵老师] - 记录课消成功: studentId=3851, subject=英语, specification=单词课, nature=正式课, hours=1.00, courseHoursId=1935378563603812352
23:09:47.573 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.StudentCourseHoursServiceImpl - [consumeHours,270] - [3850|筱灵老师] - 从课时记录消费并记录课消: recordId=1935378563603812352, batchNo=BATCH_1750265181780, nature=正式课, consumed=1.00, remaining=32.00, courseId=1952332155724603392
23:09:47.574 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.StudentCourseHoursServiceImpl - [consumeHours,274] - [3850|筱灵老师] - 课时消费完成: studentId=3851, subject=英语, specification=单词课, nature=正式课, totalConsumed=1.00, courseId=1952332155724603392
23:09:47.574 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [performCourseHoursConsumption,1738] - [3850|筱灵老师] - 课消执行成功: courseId=1952332155724603392, studentId=3851, consumedHours=1.00
23:09:49.332 [http-nio-8080-exec-51] INFO  o.n.w.c.u.ThreadPoolService - [submit,56] - [3850|筱灵老师] - 收到线程池任务: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c99b70
23:09:49.333 [http-nio-8080-exec-51] INFO  o.n.w.c.u.ThreadPoolService - [submit,56] - [3850|筱灵老师] - 收到线程池任务: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c94000
23:09:49.335 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateAndSaveErrorMaterials,3398] - [System|系统] - 开始生成课程错误内容材料, courseId: 1952332155724603392
23:09:49.335 [http-nio-8080-exec-51] INFO  o.n.w.c.u.ThreadPoolService - [submit,56] - [3850|筱灵老师] - 收到线程池任务: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c94458
23:09:49.336 [http-nio-8080-exec-51] INFO  o.n.w.s.s.i.CourseServiceImpl - [endCourse,1675] - [3850|筱灵老师] - 成功结束课程, courseId: 1952332155724603392
23:09:49.473 [words-pool-2] INFO  o.n.w.c.u.ThreadPoolService - [lambda$submit$0,64] - [System|系统] - 线程池任务执行完成: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c94458
23:09:49.490 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateErrorHandoutPdf,3105] - [System|系统] - 生成课程错误内容讲义PDF, courseId: 1952332155724603392
23:09:50.018 [words-pool-0] INFO  o.n.w.s.s.i.CourseServiceImpl - [generatePdf,2302] - [System|系统] - PDF 文件已生成：course/1952332155724603392/【北大军哥名师团神奇英语】8月4日单词讲义.pdf
23:09:50.371 [words-pool-1] INFO  o.n.w.c.u.OssService - [uploadBytes,106] - [System|系统] - 字节数组上传成功，ETag: DCC332C34BFA812F6F503D16C5721A76
23:09:50.371 [words-pool-0] INFO  o.n.w.c.u.OssService - [uploadBytes,106] - [System|系统] - 字节数组上传成功，ETag: ACA06886CC8D1144133FF8CDB7B6730C
23:09:50.372 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateAndSaveErrorMaterials,3422] - [System|系统] - 成功生成错词讲义, courseId: 1952332155724603392, url: https://tongbu-words-dev.oss-cn-shenzhen.aliyuncs.com/course/1952332155724603392/【北大军哥名师团神奇英语】2025年08月04日错词讲义.pdf.pdf
23:09:50.404 [words-pool-0] INFO  o.n.w.s.s.i.CourseServiceImpl - [generatePracticesInfoPdf,2897] - [System|系统] - 测验PDF 文件已生成：course/1952332155724603392/【北大军哥名师团神奇英语】8月4日单词练习一.pdf
23:09:50.465 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateErrorExercisePdf,3129] - [System|系统] - 生成课程错误内容练习PDF, courseId: 1952332155724603392
23:09:50.479 [words-pool-0] INFO  o.n.w.c.u.OssService - [uploadBytes,106] - [System|系统] - 字节数组上传成功，ETag: 6B1F30E98160A0DCE6BD81D85DD1182D
23:09:50.510 [words-pool-0] INFO  o.n.w.s.s.i.CourseServiceImpl - [generatePracticesInfoPdf,2897] - [System|系统] - 测验PDF 文件已生成：course/1952332155724603392/【北大军哥名师团神奇英语】8月4日单词练习二.pdf
23:09:50.559 [words-pool-0] INFO  o.n.w.c.u.OssService - [uploadBytes,106] - [System|系统] - 字节数组上传成功，ETag: 48F8FEC683441C869405D278AD93DA6B
23:09:50.584 [words-pool-0] INFO  o.n.w.s.s.i.CourseServiceImpl - [generatePdf,2327] - [System|系统] - 保存课程pdf信息成功:1952332155724603392
23:09:50.585 [words-pool-0] INFO  o.n.w.c.u.ThreadPoolService - [lambda$submit$0,64] - [System|系统] - 线程池任务执行完成: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c99b70
23:09:50.649 [words-pool-1] INFO  o.n.w.c.u.OssService - [uploadBytes,106] - [System|系统] - 字节数组上传成功，ETag: A730F0A7EB4765B709BBD209C7E4D767
23:09:50.649 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateAndSaveErrorMaterials,3434] - [System|系统] - 成功生成错题练习, courseId: 1952332155724603392, url: https://tongbu-words-dev.oss-cn-shenzhen.aliyuncs.com/course/1952332155724603392/【北大军哥名师团神奇英语】2025年08月04日错题练习.pdf.pdf
23:09:50.674 [words-pool-1] INFO  o.n.w.s.s.i.CourseServiceImpl - [generateAndSaveErrorMaterials,3449] - [System|系统] - 成功保存错误内容下载地址, courseId: 1952332155724603392, handout: true, exercise: true
23:09:50.674 [words-pool-1] INFO  o.n.w.c.u.ThreadPoolService - [lambda$submit$0,64] - [System|系统] - 线程池任务执行完成: org.nonamespace.word.server.service.impl.CourseServiceImpl$$Lambda/0x0000019b35c94000
23:09:50.881 [http-nio-8080-exec-61] INFO  o.n.w.s.s.i.CourseServiceImpl - [endCourse,1546] - [3850|筱灵老师] - 结束课程, courseId: 1952332155724603392
23:09:51.060 [http-nio-8080-exec-54] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:09:51.215 [http-nio-8080-exec-54] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseDto,176] - [3850|筱灵老师] - 课程已结束或取消, courseId: 1952332155724603392
23:09:57.890 [http-nio-8080-exec-48] INFO  o.n.w.s.s.i.TextbookService - [treeSearch,289] - [3850|筱灵老师] - 查询教材树耗时:191，查询最后学习单词耗时： 0
23:10:11.187 [http-nio-8080-exec-57] INFO  o.n.w.s.s.i.CourseServiceImpl - [courseInfo,139] - [3850|筱灵老师] - 查询课程信息, courseId: 1952332155724603392
23:10:11.366 [http-nio-8080-exec-57] INFO  o.n.w.s.s.i.CourseServiceImpl - [buildCourseDto,176] - [3850|筱灵老师] - 课程已结束或取消, courseId: 1952332155724603392
