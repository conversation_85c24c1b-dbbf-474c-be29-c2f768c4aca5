<template>
  <div class="course-query-container">
    <!-- 查询条件区域 -->
    <el-card class="query-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>课程查询</span>
          <el-button type="primary" @click="resetQuery" size="small">重置</el-button>
        </div>
      </template>

      <el-form :model="queryForm" ref="queryFormRef" label-width="80px" class="query-form">
        <!-- 第一行：基础筛选条件 -->
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="学科" prop="subject">
              <el-select v-model="queryForm.subject" placeholder="请选择学科" clearable @change="handleSubjectChange" style="width: 100%">
                <el-option label="英语" value="英语" />
                <el-option label="语文" value="语文" />
                <el-option label="数学" value="数学" />
                <el-option label="物理" value="物理" />
                <el-option label="化学" value="化学" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="课型" prop="specification">
              <el-select
                v-model="queryForm.specification"
                placeholder="请选择课型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in availableSpecifications"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型" prop="type">
              <el-select v-model="queryForm.type" placeholder="请选择类型" clearable style="width: 100%">
                <el-option label="学习课" value="学习课" />
                <el-option label="复习课" value="复习课" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性质" prop="courseType">
              <el-select
                v-model="queryForm.courseType"
                placeholder="请选择性质"
                clearable
                style="width: 100%"
              >
                <el-option label="正式课" value="正式课" />
                <el-option label="试听课" value="试听课" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：状态和人员筛选 -->
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="课程状态" prop="courseStatuses">
              <el-select
                v-model="queryForm.courseStatuses"
                placeholder="请选择状态"
                multiple
                clearable
                style="width: 100%"
              >
                <el-option label="待开始" value="待开始" />
                <el-option label="进行中" value="进行中" />
                <el-option label="已完成" value="已完成" />
                <el-option label="停课" value="停课" />
                <el-option label="调课" value="调课" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="老师" prop="teacherName">
              <el-input
                v-model="queryForm.teacherName"
                placeholder="请输入老师姓名"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学生" prop="studentName">
              <el-input
                v-model="queryForm.studentName"
                placeholder="请输入学生姓名"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="button-group">
              <el-button type="primary" @click="handleQuery" :loading="loading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：时间和异常筛选 -->
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item label="上课日期" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="异常类型" prop="exceptionTypes">
              <el-select
                v-model="queryForm.exceptionTypes"
                placeholder="请选择异常类型"
                multiple
                clearable
                style="width: 100%"
              >
                <el-option label="时长不足" value="时长不足" />
                <el-option label="不准时" value="不准时" />
                <el-option label="迟到" value="迟到" />
                <el-option label="早退" value="早退" />
                <el-option label="调课" value="调课" />
                <el-option label="停课" value="停课" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="button-group">
              <el-button @click="resetQuery">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计区域 -->
    <el-card class="statistics-card" shadow="never">
      <template #header>
        <span>统计信息</span>
      </template>

      <el-row :gutter="20">
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalStudents }}</div>
            <div class="stat-label">学生数</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalCourses }}</div>
            <div class="stat-label">排课数</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.completedCourses }}</div>
            <div class="stat-label">完课数</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.cancelledCourses }}</div>
            <div class="stat-label">停课数</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.rescheduledCourses }}</div>
            <div class="stat-label">调课数</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.antiForgetProgress }}</div>
            <div class="stat-label">抗遗忘</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalCourseConsumption }}</div>
            <div class="stat-label">课消数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 课程列表区域 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>课程列表</span>
          <!-- <div class="header-actions">
            <el-button type="primary" @click="exportData" size="small">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div> -->
        </div>
      </template>

      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        height="600"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="courseDate" label="日期" width="100" sortable="custom" />
        <el-table-column prop="subject" label="学科" width="80" />
        <el-table-column prop="specification" label="课型" width="100" />
        <el-table-column prop="type" label="类型" width="80" />
        <el-table-column prop="courseType" label="性质" width="80" />
        <el-table-column prop="courseStatus" label="课程状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.courseStatus)">
              {{ scope.row.courseStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="teacherName" label="老师" width="100" />
        <el-table-column prop="studentName" label="学生" width="100" />
        <el-table-column label="排课时间" width="120">
          <template #default="scope">
            <div>
              {{ formatTime(scope.row.scheduledStartTime) }} -
              {{ formatTime(scope.row.scheduledEndTime) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="scheduledDuration" label="排课时长" width="80">
          <template #default="scope"> {{ scope.row.scheduledDuration }}分钟 </template>
        </el-table-column>
        <el-table-column label="实际时间" width="120">
          <template #default="scope">
            <div v-if="scope.row.actualStartTime">
              {{ formatTime(scope.row.actualStartTime) }} -
              {{ formatTime(scope.row.actualEndTime) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actualDuration" label="实际时长" width="80">
          <template #default="scope">
            {{ scope.row.actualDuration ? scope.row.actualDuration + "分钟" : "-" }}
          </template>
        </el-table-column>
        <el-table-column label="抗遗忘完成情况" width="200">
          <template #default="scope">
            <div class="anti-forget-status">
              <el-tag
                size="small"
                :type="scope.row.antiForgetStatus?.d2Status?.color || 'info'"
                >D2</el-tag
              >
              <el-tag
                size="small"
                :type="scope.row.antiForgetStatus?.d4Status?.color || 'info'"
                >D4</el-tag
              >
              <el-tag
                size="small"
                :type="scope.row.antiForgetStatus?.d7Status?.color || 'info'"
                >D7</el-tag
              >
              <el-tag
                size="small"
                :type="scope.row.antiForgetStatus?.d14Status?.color || 'info'"
                >D14</el-tag
              >
              <el-tag
                size="small"
                :type="scope.row.antiForgetStatus?.d21Status?.color || 'info'"
                >D21</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="异常类型" width="120">
          <template #default="scope">
            <div v-if="scope.row.exceptionTypes && scope.row.exceptionTypes.length > 0">
              <el-tag
                v-for="exception in scope.row.exceptionTypes"
                :key="exception"
                size="small"
                type="warning"
                style="margin: 2px"
              >
                {{ exception }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseConsumption" label="课消" width="80">
          <template #default="scope">
            {{ scope.row.courseConsumption ? scope.row.courseConsumption + "小时" : "-" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 开始上课按钮 -->
              <el-button
                v-if="canStartCourse(scope.row)"
                type="primary"
                size="small"
                :icon="VideoPlay"
                :loading="getLoading(scope.row.id, 'start')"
                @click="handleStart(scope.row)"
              >
                开始上课
              </el-button>

              <!-- 调课按钮 -->
              <el-button
                v-if="canReschedule(scope.row)"
                type="warning"
                size="small"
                :icon="Edit"
                :loading="getLoading(scope.row.id, 'reschedule')"
                @click="handleReschedule(scope.row)"
              >
                调课
              </el-button>

              <!-- 进入课堂按钮 -->
              <el-button
                v-if="canEnterCourse(scope.row)"
                type="primary"
                size="small"
                :icon="VideoPlay"
                :loading="getLoading(scope.row.id, 'start')"
                @click="handleStart(scope.row)"
              >
                进入课堂
              </el-button>

              <!-- 查看课程按钮 -->
              <el-button
                v-if="canViewCourse(scope.row)"
                type="primary"
                size="small"
                :icon="VideoPlay"
                @click="handleViewCourse(scope.row)"
              >
                查看课程
              </el-button>

              <!-- 课程资料下载下拉菜单 -->
              <el-dropdown
                v-if="canDownloadMaterial(scope.row)"
                trigger="click"
                @command="(command) => handleDownload(scope.row, command)"
                :loading="getLoading(scope.row.id, 'download')"
              >
                <el-button
                  type="success"
                  size="small"
                  :loading="getLoading(scope.row.id, 'download')"
                >
                  课程资料
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="handout">
                      <el-icon><Document /></el-icon>
                      下载讲义
                    </el-dropdown-item>
                    <el-dropdown-item command="exercise1">
                      <el-icon><EditPen /></el-icon>
                      下载练习1
                    </el-dropdown-item>
                    <el-dropdown-item command="exercise2">
                      <el-icon><EditPen /></el-icon>
                      下载练习2
                    </el-dropdown-item>
                    <el-dropdown-item divided command="error_handout">
                      <el-icon><Warning /></el-icon>
                      错词讲义
                    </el-dropdown-item>
                    <el-dropdown-item command="error_exercise">
                      <el-icon><Warning /></el-icon>
                      错题练习
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 停课按钮 -->
              <el-button
                v-if="canCancel(scope.row)"
                type="danger"
                size="small"
                :icon="Close"
                :loading="getLoading(scope.row.id, 'cancel')"
                @click="handleCancel(scope.row)"
              >
                停课
              </el-button>

              <!-- 查看报告按钮 -->
              <el-button
                v-if="canViewReport(scope.row)"
                type="info"
                size="small"
                @click="handleViewReport(scope.row)"
              >
                查看报告
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="queryForm.pageNum"
          :page-size="queryForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 调课弹窗 -->
    <RescheduleDialog
      v-if="rescheduleDialogVisible && selectedCourse"
      v-model="rescheduleDialogVisible"
      :course="selectedCourse"
      @success="handleRescheduleSuccess"
    />
  </div>
</template>

<script setup lang="ts" name="CourseQuery">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, formatter } from 'element-plus'
import { Search, Refresh, ArrowDown, Document, EditPen, VideoPlay, Edit, Close, Check, Warning } from '@element-plus/icons-vue'
import { useCurriculumStore } from '../../stores/curriculum.js'
import * as RescheduleDialogModule from '../curriculum/components/RescheduleDialog.vue'
const RescheduleDialog = (RescheduleDialogModule as any).default || RescheduleDialogModule
import {
  queryCourses,
  getStatistics,
  viewReport,
  type CourseQueryRequest,
  type CourseQueryResponse,
  type StatisticsResponse
} from '../../api/course-query/index'

// 响应式数据
const loading = ref(false)
const queryFormRef = ref()
const dateRange = ref<[string, string] | null>(null)

// 使用课表Store
const curriculumStore = useCurriculumStore()

// 调课相关状态
const rescheduleDialogVisible = ref(false)
const selectedCourse = ref<CourseQueryResponse | null>(null)

// 操作加载状态管理
const operationLoading = ref<Record<string, Record<string, boolean>>>({})

// 获取操作加载状态
const getLoading = (courseId: string, operation: string) => {
  return operationLoading.value[courseId]?.[operation] || false
}

// 设置操作加载状态
const setLoading = (courseId: string, operation: string, isLoading: boolean) => {
  if (!operationLoading.value[courseId]) {
    operationLoading.value[courseId] = {}
  }
  operationLoading.value[courseId][operation] = isLoading
}

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject: string) => {
  // 清空当前选择的课型
  queryForm.specification = undefined

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询表单
const queryForm = reactive<CourseQueryRequest>({
  pageNum: 1,
  pageSize: 10,
  orderBy: 'scheduledStartTime',
  orderDirection: 'desc'
})

// 表格数据
const tableData = ref<CourseQueryResponse[]>([])
const total = ref(0)

// 统计数据
const statistics = ref<StatisticsResponse>({
  totalStudents: 0,
  totalCourses: 0,
  completedCourses: 0,
  cancelledCourses: 0,
  rescheduledCourses: 0,
  antiForgetProgress: '0/0',
  totalCourseConsumption: 0
})

// 初始化最近7天的时间范围
const initDefaultTimeRange = () => {
  const today = new Date()
  const sevenDaysAgo = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000) // 最近7天（包含今天）

  const startDate = sevenDaysAgo.toISOString().split('T')[0]
  const endDate = today.toISOString().split('T')[0]

  dateRange.value = [startDate, endDate]
  queryForm.startDate = startDate
  queryForm.endDate = endDate
}

// 页面加载时执行
onMounted(() => {
  initDefaultTimeRange()
  handleQuery()
})

// 查询数据
const handleQuery = async () => {
  loading.value = true
  try {
    // 同时获取列表数据和统计数据
    const [listResult, statsResult] = await Promise.all([
      queryCourses(queryForm),
      getStatistics(queryForm)
    ])

    if (listResult.code === 200) {
      tableData.value = listResult.rows || []
      total.value = listResult.total || 0
    }

    if (statsResult.code === 200) {
      statistics.value = statsResult.data
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  // 重置时恢复默认的最近7天时间范围
  initDefaultTimeRange()
  queryForm.pageNum = 1
  handleQuery()
}

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    queryForm.startDate = dates[0]
    queryForm.endDate = dates[1]
  } else {
    queryForm.startDate = undefined
    queryForm.endDate = undefined
  }
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryForm.pageSize = size
  queryForm.pageNum = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryForm.pageNum = page
  handleQuery()
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryForm.orderBy = prop
  queryForm.orderDirection = order === 'ascending' ? 'asc' : 'desc'
  handleQuery()
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待开始': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '停课': 'danger',
    '调课': 'warning'
  }
  return statusMap[status] || 'info'
}

// 格式化时间
const formatTime = (timeString) => {
  if(!timeString) return ''
  const date = new Date(timeString);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};


// 按钮显示控制方法 - 完全按照课表逻辑
const canStartCourse = (row: CourseQueryResponse) => {
//   return row.courseStatus === '待开始'
    return row.canStart
}

const canEnterCourse = (row: CourseQueryResponse) => {
  return row.canEnter && row.courseStatus === '进行中'
}

const canViewCourse = (row: CourseQueryResponse) => {
  return row.canEnter && row.courseStatus === '已完成'
}

const canReschedule = (row: CourseQueryResponse) => {
  return row.canReschedule
}

const canCancel = (row: CourseQueryResponse) => {
  return row.canCancel
}

const canDownloadMaterial = (row: CourseQueryResponse) => {
  return row.canDownloadMaterial
}

const canViewReport = (row: CourseQueryResponse) => {
  return row.canViewReport
}

// 操作处理函数 - 完全复用课表Store的方法
const handleStart = async (row: CourseQueryResponse) => {
  setLoading(row.id, 'start', true)
  try {
    const success = await curriculumStore.startCourse(row.id)
    if (success) {
      handleQuery() // 刷新列表
    }
  } finally {
    setLoading(row.id, 'start', false)
  }
}

const handleViewCourse = (row: CourseQueryResponse) => {
  // 查看课程 - 打开课程页面
  window.open(`/course/index?courseId=${row.id}`, '_blank')
}

const handleReschedule = (row: CourseQueryResponse) => {
  // 转换数据格式，确保包含 duration 字段
  const courseForReschedule = {
    ...row,
    duration: row.scheduledDuration, // 将 scheduledDuration 映射为 duration
    startTime: row.scheduledStartTime, // 将 scheduledStartTime 映射为 startTime
    endTime: row.scheduledEndTime // 将 scheduledEndTime 映射为 endTime
  }
  selectedCourse.value = courseForReschedule
  rescheduleDialogVisible.value = true
}

const handleRescheduleSuccess = () => {
  rescheduleDialogVisible.value = false
  selectedCourse.value = null
  handleQuery() // 刷新列表
}

const handleConsume = async (row: CourseQueryResponse) => {
  setLoading(row.id, 'consume', true)
  try {
    const success = await curriculumStore.consumeCourse({
      courseId: row.id,
      // 其他消课需要的参数
    })
    if (success) {
      handleQuery() // 刷新列表
    }
  } finally {
    setLoading(row.id, 'consume', false)
  }
}

const handleCancel = async (row: CourseQueryResponse) => {
  try {
    // 创建自定义停课表单HTML
    const cancelFormHtml = `
      <div class="cancel-course-form">
        <div class="form-group">
          <label class="form-label">停课类型：</label>
          <div class="radio-group">
            <label class="radio-item">
              <input type="radio" name="cancelType" value="teacher" checked>
              <span class="radio-text">老师原因</span>
            </label>
            <label class="radio-item">
              <input type="radio" name="cancelType" value="student">
              <span class="radio-text">学生原因</span>
            </label>
          </div>
        </div>
        <div class="form-group">
          <label class="form-label">停课说明：</label>
          <textarea
            id="cancelReason"
            class="form-textarea"
            placeholder="请详细说明停课原因..."
            rows="3"
          ></textarea>
        </div>
      </div>
    `

    let cancelData = { type: "teacher", reason: "" }

    await ElMessageBox({
      title: "停课确认",
      message: cancelFormHtml,
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确定停课",
      cancelButtonText: "取消",
      type: "warning",
      customClass: "cancel-course-dialog",
      beforeClose: (action, _instance, done) => {
        if (action === "confirm") {
          try {
            // 直接查询DOM，因为表单已经渲染在当前页面中
            const selectedType = (document.querySelector(
              '.cancel-course-dialog input[name="cancelType"]:checked'
            ) as HTMLInputElement)?.value
            const reason = (document
              .querySelector(".cancel-course-dialog #cancelReason") as HTMLTextAreaElement)
              ?.value?.trim()

            if (!reason) {
              ElMessage.warning("请填写停课说明")
              return false
            }

            // 更新外部的cancelData变量
            cancelData = {
              type: selectedType || "teacher",
              reason: reason,
            }
          } catch (error) {
            console.error("获取停课表单数据失败:", error)
            ElMessage.error("获取表单数据失败")
            return false
          }
        }
        done()
      },
    })

    setLoading(row.id, 'cancel', true)

    const success = await curriculumStore.cancelCourse(row.id, cancelData)
    if (success) {
      handleQuery() // 刷新列表
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.log('用户取消停课操作或发生错误:', error)
    }
  } finally {
    setLoading(row.id, 'cancel', false)
  }
}

const handleDownload = async (row: CourseQueryResponse, materialType: string = 'handout') => {
  setLoading(row.id, 'download', true)
  try {
    await curriculumStore.downloadCourseMaterial(row.id, materialType)
    // curriculumStore.downloadCourseMaterial 内部已经处理了成功消息
  } catch (error) {
    console.error('下载课程资料失败:', error)
  } finally {
    setLoading(row.id, 'download', false)
  }
}

const handleViewReport = async (row: CourseQueryResponse) => {
  try {
    await viewReport(row.id)
    ElMessage.success('报告查看成功')
  } catch (error) {
    ElMessage.error('报告查看失败')
  }
}
</script>

<style scoped>
.course-query-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.query-card,
.statistics-card,
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 查询表单样式优化 */
.query-form {
  padding: 10px 0;
}

.query-form .el-form-item {
  margin-bottom: 16px;
}

.query-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.query-form .button-group {
  text-align: left;
  margin-bottom: 0;
}

.query-form .button-group .el-button {
  margin-right: 12px;
}

/* 确保所有输入框宽度一致 */
.query-form .el-select,
.query-form .el-input,
.query-form .el-date-picker {
  width: 100%;
}

/* 日期选择器特殊处理 */
.query-form .el-date-picker.el-range-editor {
  width: 100% !important;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 统计区域样式 */
.stat-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 抗遗忘状态样式 */
.anti-forget-status {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 表格样式优化 */
.el-table {
  font-size: 13px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 查询表单样式 */
.el-form-item {
  margin-bottom: 16px;
}

.el-form-item__label {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-item {
    padding: 15px;
  }

  .stat-value {
    font-size: 24px;
  }

  .action-buttons .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 中等屏幕：保持3行布局，稍微调整间距 */
  .query-form .el-row {
    margin-bottom: 8px;
  }
}

@media (max-width: 768px) {
  .course-query-container {
    padding: 10px;
  }

  /* 平板端：每行显示2个条件 */
  .query-form .el-row:first-child .el-col,
  .query-form .el-row:nth-child(2) .el-col:not(.button-group),
  .query-form .el-row:nth-child(3) .el-col:not(.button-group) {
    flex: 0 0 50%;
    max-width: 50%;
    margin-bottom: 12px;
  }

  /* 按钮组在平板端保持原位置 */
  .query-form .el-row:nth-child(2) .el-col:nth-child(4),
  .query-form .el-row:nth-child(3) .el-col:nth-child(3) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  /* 按钮组样式调整 */
  .query-form .button-group {
    text-align: left;
  }

  .el-table {
    font-size: 12px;
  }

  .stat-item {
    padding: 10px;
  }

  .stat-value {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  /* 小屏幕：每行显示1个条件 */
  .query-form .el-row .el-col {
    flex: 0 0 100% !important;
    max-width: 100% !important;
    margin-bottom: 12px;
  }

  /* 按钮组在小屏幕居中显示 */
  .query-form .button-group {
    text-align: center;
    margin-top: 8px;
  }

  .query-form .button-group .el-button {
    margin: 0 6px;
  }
}

/* 停课表单样式 */
:deep(.cancel-course-dialog) .el-message-box__content {
  padding: 20px !important;
}

:deep(.cancel-course-dialog) .el-message-box__message {
  margin: 0 !important;
}

/* 全局停课表单样式 */
:deep(.cancel-course-form) .form-group {
  margin-bottom: 20px;
}

:deep(.cancel-course-form) .form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

:deep(.cancel-course-form) .radio-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

:deep(.cancel-course-form) .radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 0;
}

:deep(.cancel-course-form) .radio-item input[type="radio"] {
  margin-right: 6px;
  width: 16px;
  height: 16px;
}

:deep(.cancel-course-form) .radio-text {
  color: #606266;
  font-size: 14px;
  user-select: none;
}

:deep(.cancel-course-form) .radio-item:hover .radio-text {
  color: #409eff;
}

:deep(.cancel-course-form) .form-textarea {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  background-color: #fff;
  resize: vertical;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  font-family: inherit;
  box-sizing: border-box;
}

:deep(.cancel-course-form) .form-textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

:deep(.cancel-course-form) .form-textarea::placeholder {
  color: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.cancel-course-form) .radio-group {
    flex-direction: column;
    gap: 12px;
  }

  :deep(.cancel-course-form) .form-textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
</style>
