import request from '@/utils/request'

/**
 * 订单管理API
 */

// 创建订单
export function createOrderApi(data) {
  return request({
    url: '/order/create',
    method: 'post',
    data
  })
}

// 根据订单ID查询订单详情
export function getOrderDetailApi(orderId) {
  return request({
    url: `/order/${orderId}`,
    method: 'get'
  })
}

// 根据订单号查询订单详情
export function getOrderByNoApi(orderNo) {
  return request({
    url: `/order/no/${orderNo}`,
    method: 'get'
  })
}

// 根据学生ID查询订单列表
export function getOrdersByStudentApi(studentId) {
  return request({
    url: `/order/student/${studentId}`,
    method: 'get'
  })
}

// 根据销售员ID查询订单列表
export function getOrdersBySalerApi(salerId) {
  return request({
    url: `/order/saler/${salerId}`,
    method: 'get'
  })
}

// 根据订单状态查询订单列表
export function getOrdersByStatusApi(orderStatus) {
  return request({
    url: `/order/status/${orderStatus}`,
    method: 'get'
  })
}

// 根据订单ID查询交易流水列表
export function getOrderTransactionsApi(orderId) {
  return request({
    url: `/order/${orderId}/transactions`,
    method: 'get'
  })
}

// 发起支付（生成支付二维码）
export function nativePayApi(orderTrxId) {
  return request({
    url: `/order/pay/${orderTrxId}`,
    method: 'post'
  })
}

// 取消订单
export function cancelOrderApi(orderId) {
  return request({
    url: `/order/${orderId}/cancel`,
    method: 'put'
  })
}

// 生成支付信息
export function generatePaymentApi(orderTrxId) {
  return request({
    url: `/order/payment/${orderTrxId}`,
    method: 'post'
  })
}

// 生成支付二维码
export function generateQRCodeApi(orderTrxId) {
  return request({
    url: `/order/qrcode/${orderTrxId}`,
    method: 'post'
  })
}

// 获取支付链接
export function getPaymentLinkApi(orderTrxId) {
  return request({
    url: `/order/payment-link/${orderTrxId}`,
    method: 'get'
  })
}
