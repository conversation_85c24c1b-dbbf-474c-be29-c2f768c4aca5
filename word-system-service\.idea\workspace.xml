<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6b6912d0-0079-494c-a501-93bcbf5eeea0" name="Changes" comment="feat：订单相关">
      <change beforePath="$PROJECT_DIR$/wss-launcher/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/wss-launcher/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$/wss-common">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/wss-common">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="wss-common" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="wss-common" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="package-info" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/v1.0.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://D:/maven_repository/cn/hutool/hutool-all/5.8.37/hutool-all-5.8.37.jar!/cn/hutool/core/bean/BeanUtil.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/baomidou/mybatis-plus-core/3.5.6/mybatis-plus-core-3.5.6.jar!/com/baomidou/mybatisplus/core/conditions/interfaces/Compare.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/baomidou/mybatis-plus-core/3.5.6/mybatis-plus-core-3.5.6.jar!/com/baomidou/mybatisplus/core/conditions/query/LambdaQueryWrapper.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/baomidou/mybatis-plus-core/3.5.6/mybatis-plus-core-3.5.6.jar!/com/baomidou/mybatisplus/core/mapper/BaseMapper.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/baomidou/mybatis-plus-extension/3.5.6/mybatis-plus-extension-3.5.6.jar!/com/baomidou/mybatisplus/extension/service/impl/ServiceImpl.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/ruoyi/ruoyi-common/3.8.7/ruoyi-common-3.8.7.jar!/com/ruoyi/common/core/controller/BaseController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/ruoyi/ruoyi-common/3.8.7/ruoyi-common-3.8.7.jar!/com/ruoyi/common/core/domain/AjaxResult.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/com/ruoyi/ruoyi-common/3.8.7/ruoyi-common-3.8.7.jar!/com/ruoyi/common/core/domain/BaseEntity.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/maven_repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar!/jakarta/validation/constraints/NotBlank.class" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/wss-common/src/main/java/org/nonamespace/word/common/utils/SentenceSplitter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/wss-launcher/src/test/java/org/nonamespace/word/launcher/test/CreateFileByTemplateTest.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/wss-rest/src/main/java/org/nonamespace/word/rest/controller/CourseController.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/wss-server/src/main/java/org/nonamespace/word/server/facade/impl/CurriculumFacadeImpl.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/wss-server/src/main/java/org/nonamespace/word/server/service/impl/CourseServiceImpl.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
        <option name="userSettingsFile" value="E:\Software\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2x07aXzoJNORGkvMm9Ms6yE8FrD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AllinPaySignUtil.executor": "Run",
    "Application.SentenceInsertPipesUtils.executor": "Run",
    "Application.SentenceSplitter.executor": "Run",
    "Application.SpecialCharHelper.executor": "Run",
    "Application.TestWordController.executor": "Run",
    "Application.WordEnrichServiceImpl.executor": "Run",
    "Application.WxSendMessageServiceImpl.executor": "Run",
    "HTTP Request.generated-requests | #1.executor": "Run",
    "JUnit.CreateFileByTemplateTest.testCreateFileByTemplate (1).executor": "Run",
    "JUnit.CreateFileByTemplateTest.testCreateFileByTemplate.executor": "Debug",
    "JUnit.CreateSignFlowByFileTest.testCreateSignFlowByFile.executor": "Run",
    "JUnit.GetSignUrlTest.testGetSignUrl (1).executor": "Debug",
    "JUnit.GetSignUrlTest.testGetSignUrl.executor": "Debug",
    "JUnit.SiliconflowServiceTest.testSiliconflowService.executor": "Run",
    "JUnit.WxSendMessageServiceTest.testEndCourseMessage.executor": "Debug",
    "JUnit.WxSendMessageServiceTest.testEndReviewMessage.executor": "Run",
    "Maven.word-system-service [clean].executor": "Run",
    "Maven.word-system-service [install].executor": "Run",
    "Maven.word-system-service [package].executor": "Run",
    "Maven.wss-common [clean].executor": "Run",
    "Maven.wss-common [install].executor": "Run",
    "Maven.wss-launcher [compile].executor": "Run",
    "Maven.wss-openai [install].executor": "Run",
    "Maven.wss-pay [install].executor": "Run",
    "Maven.wss-server [clean].executor": "Run",
    "Maven.wss-server [compile].executor": "Run",
    "Maven.wss-server [install].executor": "Run",
    "Maven.wss-thirdpart [install].executor": "Run",
    "Maven.wss-thirdpart [verify].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.RuoYiApplication.executor": "Debug",
    "Spring Boot.WssCommonApplication.executor": "Debug",
    "Spring Boot.WssLauncherApplication (1).executor": "Run",
    "Spring Boot.WssLauncherApplication.executor": "Debug",
    "git-widget-placeholder": "feature/v1.1.0",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/idea_projects/words/word-system-service/wss-launcher/src/main/resources/db/migration",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\idea_projects\words\word-system-service\wss-launcher\src\main\resources\db\migration" />
      <recent name="E:\idea_projects\word-system-service\wss-server\src\main\java\org\nonamespace\word\server\service\order\impl" />
      <recent name="E:\idea_projects\word-system-service\wss-launcher\src\test\java\org\nonamespace\word\launcher\test" />
      <recent name="E:\idea_projects\word-system-service\wss-launcher\src\main\resources\prompts" />
      <recent name="E:\idea_projects\word-system-service\wss-common\src\main\java\org\nonamespace\word\common\utils" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.nonamespace.word.launcher.test" />
      <recent name="org.nonamespace.word.thirdpart.esign.util" />
      <recent name="org.nonamespace.word.pay.model" />
      <recent name="org.nonamespace.word.common.config" />
      <recent name="org.nonamespace.word.openai.config" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.WssLauncherApplication">
    <configuration name="CreateFileByTemplateTest.testCreateFileByTemplate (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="wss-launcher" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.nonamespace.word.launcher.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.nonamespace.word.launcher.test" />
      <option name="MAIN_CLASS_NAME" value="org.nonamespace.word.launcher.test.CreateFileByTemplateTest" />
      <option name="METHOD_NAME" value="testCreateFileByTemplate" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CreateFileByTemplateTest.testCreateFileByTemplate" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="wss-thirdpart" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.nonamespace.word.thirdpart.esign.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.nonamespace.word.thirdpart.esign.service" />
      <option name="MAIN_CLASS_NAME" value="org.nonamespace.word.thirdpart.esign.service.CreateFileByTemplateTest" />
      <option name="METHOD_NAME" value="testCreateFileByTemplate" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CreateSignFlowByFileTest.testCreateSignFlowByFile" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="wss-launcher" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.nonamespace.word.launcher.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.nonamespace.word.launcher.test" />
      <option name="MAIN_CLASS_NAME" value="org.nonamespace.word.launcher.test.CreateSignFlowByFileTest" />
      <option name="METHOD_NAME" value="testCreateSignFlowByFile" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GetSignUrlTest.testGetSignUrl (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="wss-launcher" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.nonamespace.word.launcher.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.nonamespace.word.launcher.test" />
      <option name="MAIN_CLASS_NAME" value="org.nonamespace.word.launcher.test.GetSignUrlTest" />
      <option name="METHOD_NAME" value="testGetSignUrl" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GetSignUrlTest.testGetSignUrl" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="wss-thirdpart" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.nonamespace.word.thirdpart.esign.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.nonamespace.word.thirdpart.esign.service" />
      <option name="MAIN_CLASS_NAME" value="org.nonamespace.word.thirdpart.esign.service.GetSignUrlTest" />
      <option name="METHOD_NAME" value="testGetSignUrl" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WssLauncherApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wss-launcher" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.nonamespace.word.launcher.WssLauncherApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.CreateSignFlowByFileTest.testCreateSignFlowByFile" />
      <item itemvalue="JUnit.GetSignUrlTest.testGetSignUrl" />
      <item itemvalue="JUnit.GetSignUrlTest.testGetSignUrl (1)" />
      <item itemvalue="JUnit.CreateFileByTemplateTest.testCreateFileByTemplate" />
      <item itemvalue="JUnit.CreateFileByTemplateTest.testCreateFileByTemplate (1)" />
      <item itemvalue="Spring Boot.WssLauncherApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.CreateSignFlowByFileTest.testCreateSignFlowByFile" />
        <item itemvalue="JUnit.CreateFileByTemplateTest.testCreateFileByTemplate (1)" />
        <item itemvalue="JUnit.CreateFileByTemplateTest.testCreateFileByTemplate" />
        <item itemvalue="JUnit.GetSignUrlTest.testGetSignUrl (1)" />
        <item itemvalue="JUnit.GetSignUrlTest.testGetSignUrl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.25659.39" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.25659.39" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Physical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6b6912d0-0079-494c-a501-93bcbf5eeea0" name="Changes" comment="" />
      <created>1747063201929</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747063201929</updated>
      <workItem from="1747063202869" duration="7054000" />
      <workItem from="1747138869515" duration="14193000" />
      <workItem from="1747222068196" duration="2679000" />
      <workItem from="1747224802675" duration="16166000" />
      <workItem from="1747395846407" duration="4537000" />
      <workItem from="1747406354278" duration="5720000" />
      <workItem from="1747412666874" duration="819000" />
      <workItem from="1747413765027" duration="1056000" />
      <workItem from="1747447997908" duration="25305000" />
      <workItem from="1747491499551" duration="11659000" />
      <workItem from="1747504043872" duration="578000" />
      <workItem from="1747533423269" duration="2147000" />
      <workItem from="1747540239427" duration="20442000" />
      <workItem from="1747572978468" duration="2948000" />
      <workItem from="1747656066325" duration="2610000" />
      <workItem from="1747669912504" duration="901000" />
      <workItem from="1747740892200" duration="893000" />
      <workItem from="1747745397812" duration="13448000" />
      <workItem from="1747761010621" duration="406000" />
      <workItem from="1747830482875" duration="1813000" />
      <workItem from="1747833485280" duration="64000" />
      <workItem from="1747833566100" duration="1938000" />
      <workItem from="1747835666267" duration="679000" />
      <workItem from="1747837535088" duration="4052000" />
      <workItem from="1747841715355" duration="1018000" />
      <workItem from="1747869648819" duration="2145000" />
      <workItem from="1747871888242" duration="4000" />
      <workItem from="1747916361668" duration="2491000" />
      <workItem from="1747920018114" duration="8737000" />
      <workItem from="1747976406475" duration="22030000" />
      <workItem from="1748089952170" duration="11126000" />
      <workItem from="1748138449462" duration="1427000" />
      <workItem from="1748141335916" duration="27765000" />
      <workItem from="1748261328898" duration="9130000" />
      <workItem from="1748273330618" duration="1453000" />
      <workItem from="1748276149846" duration="813000" />
      <workItem from="1748312844614" duration="1878000" />
      <workItem from="1748346100304" duration="12669000" />
      <workItem from="1748430851036" duration="6519000" />
      <workItem from="1748490075671" duration="18502000" />
      <workItem from="1748610874404" duration="10699000" />
      <workItem from="1748678668727" duration="24312000" />
      <workItem from="1748839225573" duration="124000" />
      <workItem from="1748839717809" duration="1237000" />
      <workItem from="1748859115114" duration="15820000" />
      <workItem from="1748878888669" duration="6578000" />
      <workItem from="1748953602458" duration="1898000" />
      <workItem from="1748957261872" duration="4393000" />
      <workItem from="1748967194877" duration="1674000" />
      <workItem from="1749039617736" duration="6953000" />
      <workItem from="1749125624888" duration="9066000" />
      <workItem from="1749134925521" duration="3997000" />
      <workItem from="1749209637660" duration="2802000" />
      <workItem from="1749214023176" duration="5815000" />
      <workItem from="1749222338980" duration="600000" />
      <workItem from="1749305749566" duration="11978000" />
      <workItem from="1749350757078" duration="4861000" />
      <workItem from="1749474333120" duration="10686000" />
      <workItem from="1749559001591" duration="14942000" />
      <workItem from="1749643199564" duration="4863000" />
      <workItem from="1749653470079" duration="9684000" />
      <workItem from="1749664868912" duration="122000" />
      <workItem from="1749730823306" duration="14532000" />
      <workItem from="1749813524737" duration="16462000" />
      <workItem from="1749873276131" duration="10741000" />
      <workItem from="1749918919796" duration="1357000" />
      <workItem from="1749993298468" duration="11477000" />
      <workItem from="1750079329553" duration="1200000" />
      <workItem from="1750426738422" duration="4872000" />
      <workItem from="1750477328387" duration="17977000" />
      <workItem from="1750557439762" duration="25891000" />
      <workItem from="1750601051963" duration="49000" />
      <workItem from="1750601372872" duration="123000" />
      <workItem from="1751075349093" duration="75000" />
      <workItem from="1751167102073" duration="2622000" />
      <workItem from="1751541995933" duration="15000" />
      <workItem from="1751548890416" duration="9014000" />
      <workItem from="1751586362034" duration="8189000" />
      <workItem from="1751636443236" duration="625000" />
      <workItem from="1751974190024" duration="4117000" />
      <workItem from="1752239858691" duration="1157000" />
      <workItem from="1752288559114" duration="248000" />
      <workItem from="1752288908567" duration="364000" />
      <workItem from="1752498344405" duration="865000" />
      <workItem from="1752912594535" duration="1532000" />
      <workItem from="1753703741404" duration="5117000" />
      <workItem from="1753709074023" duration="3588000" />
      <workItem from="1753749522186" duration="30372000" />
      <workItem from="1753877969241" duration="7736000" />
      <workItem from="1753965853033" duration="2410000" />
      <workItem from="1754052986469" duration="4683000" />
      <workItem from="1754133200762" duration="10716000" />
      <workItem from="1754186876607" duration="8072000" />
      <workItem from="1754199304377" duration="5442000" />
      <workItem from="1754221366038" duration="1871000" />
      <workItem from="1754312179256" duration="8752000" />
      <workItem from="1754322373676" duration="126000" />
      <workItem from="1754397788994" duration="658000" />
      <workItem from="1754400184816" duration="67000" />
      <workItem from="1754400277559" duration="7540000" />
    </task>
    <task id="LOCAL-00090" summary="feat： 下课通知，增加词汇统计">
      <option name="closed" value="true" />
      <created>1749660464210</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1749660464210</updated>
    </task>
    <task id="LOCAL-00091" summary="feat： 下课通知，增加词汇统计">
      <option name="closed" value="true" />
      <created>1749662673631</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1749662673631</updated>
    </task>
    <task id="LOCAL-00092" summary="feat：增加判空处理">
      <option name="closed" value="true" />
      <created>1749819190066</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1749819190067</updated>
    </task>
    <task id="LOCAL-00093" summary="feat：增加tbi ids的打印">
      <option name="closed" value="true" />
      <created>1749820578474</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1749820578474</updated>
    </task>
    <task id="LOCAL-00094" summary="feat：兼容例句音频为&quot;&quot;字符串的问题">
      <option name="closed" value="true" />
      <created>1749825095667</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1749825095667</updated>
    </task>
    <task id="LOCAL-00095" summary="feat：兼容例句音频为&quot;&quot;字符串的问题">
      <option name="closed" value="true" />
      <created>1749825571778</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1749825571778</updated>
    </task>
    <task id="LOCAL-00096" summary="feat：单词补全增加nebius选项">
      <option name="closed" value="true" />
      <created>1749826024928</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1749826024928</updated>
    </task>
    <task id="LOCAL-00097" summary="feat：word的version默认1">
      <option name="closed" value="true" />
      <created>1749831610106</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1749831610106</updated>
    </task>
    <task id="LOCAL-00098" summary="feat：新增正课 下课推送复习列表。拓展单词数量">
      <option name="closed" value="true" />
      <created>1750004875475</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1750004875476</updated>
    </task>
    <task id="LOCAL-00099" summary="feat： 下课推送，增加老师推送">
      <option name="closed" value="true" />
      <created>1750429479229</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1750429479229</updated>
    </task>
    <task id="LOCAL-00100" summary="feat：微调下提示词">
      <option name="closed" value="true" />
      <created>1750478700629</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1750478700630</updated>
    </task>
    <task id="LOCAL-00101" summary="fix： 推送报告用户名称改为nickname">
      <option name="closed" value="true" />
      <created>1750484707565</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1750484707565</updated>
    </task>
    <task id="LOCAL-00102" summary="fix： 微调提示词，例句区分不同的stage">
      <option name="closed" value="true" />
      <created>1750507219349</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1750507219349</updated>
    </task>
    <task id="LOCAL-00103" summary="fix：微调补全时，meanings和sentences为空的问题">
      <option name="closed" value="true" />
      <created>1750509209806</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1750509209806</updated>
    </task>
    <task id="LOCAL-00104" summary="fix：调整下补全的日志">
      <option name="closed" value="true" />
      <created>1750509676389</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1750509676390</updated>
    </task>
    <task id="LOCAL-00105" summary="fix：调整下补全的日志">
      <option name="closed" value="true" />
      <created>1750509717788</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1750509717788</updated>
    </task>
    <task id="LOCAL-00106" summary="fix：修改调度逻辑，如果补全失败的，当天内不会重新补全">
      <option name="closed" value="true" />
      <created>1750511091105</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1750511091105</updated>
    </task>
    <task id="LOCAL-00107" summary="feat：增加单词和释义分开补全逻辑；增加补全失败后redis锁30分钟；增加补全失败后数据库记录">
      <option name="closed" value="true" />
      <created>1750517808515</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1750517808515</updated>
    </task>
    <task id="LOCAL-00108" summary="feat：增加单词和释义分开补全逻辑；增加补全失败后redis锁30分钟；增加补全失败后数据库记录">
      <option name="closed" value="true" />
      <created>1750517905739</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1750517905739</updated>
    </task>
    <task id="LOCAL-00109" summary="feat：增加分阶段补全逻辑">
      <option name="closed" value="true" />
      <created>1750578015237</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1750578015237</updated>
    </task>
    <task id="LOCAL-00110" summary="feat：调整补全逻辑。释义和例句为空，全套服务">
      <option name="closed" value="true" />
      <created>1750587005319</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1750587005319</updated>
    </task>
    <task id="LOCAL-00111" summary="feat：数据库最大连接数调到100">
      <option name="closed" value="true" />
      <created>1750601489085</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1750601489085</updated>
    </task>
    <task id="LOCAL-00112" summary="feat： 屏蔽匿名注解">
      <option name="closed" value="true" />
      <created>1751169737934</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1751169737935</updated>
    </task>
    <task id="LOCAL-00113" summary="feat：单词例句增加所有的阶段">
      <option name="closed" value="true" />
      <created>1751551380834</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1751551380834</updated>
    </task>
    <task id="LOCAL-00114" summary="fix：1、单词补全，新增释义和例句选项小于3个的单词&#10;2、修复学习课只上了复习课，导致下课微信消息推送异常问题">
      <option name="closed" value="true" />
      <created>1751977068852</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1751977068852</updated>
    </task>
    <task id="LOCAL-00115" summary="fix：微信消息推送给老师和学生时，username均为学生">
      <option name="closed" value="true" />
      <created>1752240939845</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1752240939846</updated>
    </task>
    <task id="LOCAL-00116" summary="feat：民生支付接口对接">
      <option name="closed" value="true" />
      <created>1753763765158</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1753763765159</updated>
    </task>
    <task id="LOCAL-00117" summary="feat：民生支付接口对接">
      <option name="closed" value="true" />
      <created>1753768969570</created>
      <option name="number" value="00117" />
      <option name="presentableId" value="LOCAL-00117" />
      <option name="project" value="LOCAL" />
      <updated>1753768969570</updated>
    </task>
    <task id="LOCAL-00118" summary="feat：民生支付接口对接">
      <option name="closed" value="true" />
      <created>1753790512889</created>
      <option name="number" value="00118" />
      <option name="presentableId" value="LOCAL-00118" />
      <option name="project" value="LOCAL" />
      <updated>1753790512889</updated>
    </task>
    <task id="LOCAL-00119" summary="fix：修复字符串内容包含nbsp的问题">
      <option name="closed" value="true" />
      <created>1753793061021</created>
      <option name="number" value="00119" />
      <option name="presentableId" value="LOCAL-00119" />
      <option name="project" value="LOCAL" />
      <updated>1753793061021</updated>
    </task>
    <task id="LOCAL-00120" summary="fix：修复字符串内容包含nbsp的问题">
      <option name="closed" value="true" />
      <created>1753793099730</created>
      <option name="number" value="00120" />
      <option name="presentableId" value="LOCAL-00120" />
      <option name="project" value="LOCAL" />
      <updated>1753793099730</updated>
    </task>
    <task id="LOCAL-00121" summary="feat：增加统一撤销 &amp; 退款接口">
      <option name="closed" value="true" />
      <created>1753796756717</created>
      <option name="number" value="00121" />
      <option name="presentableId" value="LOCAL-00121" />
      <option name="project" value="LOCAL" />
      <updated>1753796756717</updated>
    </task>
    <task id="LOCAL-00122" summary="feat：增加统一撤销 &amp; 退款接口">
      <option name="closed" value="true" />
      <created>1753799958683</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1753799958683</updated>
    </task>
    <task id="LOCAL-00123" summary="feat：支付接口回调">
      <option name="closed" value="true" />
      <created>1753881059473</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1753881059473</updated>
    </task>
    <task id="LOCAL-00124" summary="feat：交易查询 &amp; 关闭接口">
      <option name="closed" value="true" />
      <created>1753881974449</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1753881974450</updated>
    </task>
    <task id="LOCAL-00125" summary="feat：交易查询 &amp; 关闭接口">
      <option name="closed" value="true" />
      <created>1753882054559</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1753882054559</updated>
    </task>
    <task id="LOCAL-00126" summary="feat：交易接口增加records记录">
      <option name="closed" value="true" />
      <created>1753885169022</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1753885169022</updated>
    </task>
    <task id="LOCAL-00127" summary="feat： 订单和交易流水">
      <option name="closed" value="true" />
      <created>1753886107538</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1753886107538</updated>
    </task>
    <task id="LOCAL-00128" summary="feat：新增thirdpart模块">
      <option name="closed" value="true" />
      <created>1754053513469</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1754053513469</updated>
    </task>
    <task id="LOCAL-00129" summary="feat：e签宝">
      <option name="closed" value="true" />
      <created>1754057509150</created>
      <option name="number" value="00129" />
      <option name="presentableId" value="LOCAL-00129" />
      <option name="project" value="LOCAL" />
      <updated>1754057509150</updated>
    </task>
    <task id="LOCAL-00130" summary="feat：本地骑不动了">
      <option name="closed" value="true" />
      <created>1754057665480</created>
      <option name="number" value="00130" />
      <option name="presentableId" value="LOCAL-00130" />
      <option name="project" value="LOCAL" />
      <updated>1754057665480</updated>
    </task>
    <task id="LOCAL-00131" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754143840220</created>
      <option name="number" value="00131" />
      <option name="presentableId" value="LOCAL-00131" />
      <option name="project" value="LOCAL" />
      <updated>1754143840221</updated>
    </task>
    <task id="LOCAL-00132" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754192954659</created>
      <option name="number" value="00132" />
      <option name="presentableId" value="LOCAL-00132" />
      <option name="project" value="LOCAL" />
      <updated>1754192954660</updated>
    </task>
    <task id="LOCAL-00133" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754201197104</created>
      <option name="number" value="00133" />
      <option name="presentableId" value="LOCAL-00133" />
      <option name="project" value="LOCAL" />
      <updated>1754201197105</updated>
    </task>
    <task id="LOCAL-00134" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754204153383</created>
      <option name="number" value="00134" />
      <option name="presentableId" value="LOCAL-00134" />
      <option name="project" value="LOCAL" />
      <updated>1754204153383</updated>
    </task>
    <task id="LOCAL-00135" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754315077142</created>
      <option name="number" value="00135" />
      <option name="presentableId" value="LOCAL-00135" />
      <option name="project" value="LOCAL" />
      <updated>1754315077143</updated>
    </task>
    <task id="LOCAL-00136" summary="feat：e签宝，合同文件填充对接">
      <option name="closed" value="true" />
      <created>1754317533496</created>
      <option name="number" value="00136" />
      <option name="presentableId" value="LOCAL-00136" />
      <option name="project" value="LOCAL" />
      <updated>1754317533496</updated>
    </task>
    <task id="LOCAL-00137" summary="feat：订单相关">
      <option name="closed" value="true" />
      <created>1754404007006</created>
      <option name="number" value="00137" />
      <option name="presentableId" value="LOCAL-00137" />
      <option name="project" value="LOCAL" />
      <updated>1754404007008</updated>
    </task>
    <task id="LOCAL-00138" summary="feat：订单相关">
      <option name="closed" value="true" />
      <created>1754408332443</created>
      <option name="number" value="00138" />
      <option name="presentableId" value="LOCAL-00138" />
      <option name="project" value="LOCAL" />
      <updated>1754408332443</updated>
    </task>
    <option name="localTasksCounter" value="139" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/feature/v1.1.0" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix： 推送报告用户名称改为nickname" />
    <MESSAGE value="fix： 微调提示词，例句区分不同的stage" />
    <MESSAGE value="fix：微调补全时，meanings和sentences为空的问题" />
    <MESSAGE value="fix：调整下补全的日志" />
    <MESSAGE value="fix：修改调度逻辑，如果补全失败的，当天内不会重新补全" />
    <MESSAGE value="feat：增加单词和释义分开补全逻辑；增加补全失败后redis锁30分钟；增加补全失败后数据库记录" />
    <MESSAGE value="feat：增加分阶段补全逻辑" />
    <MESSAGE value="feat：调整补全逻辑。释义和例句为空，全套服务" />
    <MESSAGE value="feat：数据库最大连接数调到100" />
    <MESSAGE value="feat： 屏蔽匿名注解" />
    <MESSAGE value="feat：单词例句增加所有的阶段" />
    <MESSAGE value="fix：1、单词补全，新增释义和例句选项小于3个的单词&#10;2、修复学习课只上了复习课，导致下课微信消息推送异常问题" />
    <MESSAGE value="fix：微信消息推送给老师和学生时，username均为学生" />
    <MESSAGE value="feat：民生支付接口对接" />
    <MESSAGE value="fix：修复字符串内容包含nbsp的问题" />
    <MESSAGE value="feat：增加统一撤销 &amp; 退款接口" />
    <MESSAGE value="feat：支付接口回调" />
    <MESSAGE value="feat：交易查询 &amp; 关闭接口" />
    <MESSAGE value="feat：交易接口增加records记录" />
    <MESSAGE value="feat： 订单和交易流水" />
    <MESSAGE value="feat：新增thirdpart模块" />
    <MESSAGE value="feat：e签宝" />
    <MESSAGE value="feat：本地骑不动了" />
    <MESSAGE value="feat：e签宝，合同文件填充对接" />
    <MESSAGE value="feat：订单相关" />
    <option name="LAST_COMMIT_MESSAGE" value="feat：订单相关" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>jar://D:/maven_repository/com/ruoyi/ruoyi-admin/3.8.7/ruoyi-admin-3.8.7.jar!/BOOT-INF/classes/com/ruoyi/web/controller/common/CaptchaController.class</url>
          <line>46</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://D:/maven_repository/org/springframework/ai/spring-ai-core/1.0.0-M6/spring-ai-core-1.0.0-M6-sources.jar!/org/springframework/ai/chat/prompt/PromptTemplate.java</url>
          <line>50</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wss-wechat/src/main/java/org/nonamespace/word/wechat/controller/ParentsMpBindController.java</url>
          <line>97</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wss-server/src/main/java/org/nonamespace/word/server/service/impl/WxSendMessageServiceImpl.java</url>
          <line>298</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wss-server/src/main/java/org/nonamespace/word/server/service/impl/WxSendMessageServiceImpl.java</url>
          <line>673</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wss-server/src/main/java/org/nonamespace/word/server/service/impl/WxSendMessageServiceImpl.java</url>
          <line>152</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="objectMapper.readValue(jsonContent, WordInfo.class)" language="JAVA" />
        <watch expression="URLEncoder.encode(word, StandardCharsets.UTF_8)" language="JAVA" />
        <watch expression="step" />
        <watch expression="practices" />
        <watch expression="wordInfo" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>