<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TeacherTimeSlotMapper">

    <resultMap id="TimeSlotRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$TimeSlotResp">
        <result column="id" property="id"/>
        <result column="teacher_id" property="teacherId"/>
        <result column="weekday" property="weekday"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 查询教师的时间表 -->
    <select id="selectByTeacherId" resultMap="TimeSlotRespMap">
        SELECT
            id,
            teacher_id,
            weekday,
            start_time,
            end_time,
            status,
            remark
        FROM teacher_time_slot
        WHERE teacher_id = #{teacherId} AND deleted = false
        ORDER BY weekday, start_time
    </select>

    <!-- 批量查询教师时间段 -->
    <select id="selectByTeacherIds" resultMap="TimeSlotRespMap">
        SELECT
            id,
            teacher_id,
            weekday,
            start_time,
            end_time,
            status,
            remark
        FROM teacher_time_slot
        WHERE deleted = false
        AND teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}::int8
        </foreach>
        ORDER BY teacher_id, weekday, start_time
    </select>

    <!-- 删除教师的所有时间表 -->
    <update id="deleteByTeacherId">
        UPDATE teacher_time_slot
        SET deleted = true, update_time = CURRENT_TIMESTAMP
        WHERE teacher_id = #{teacherId}
    </update>

    <!-- 批量插入教师时间表 -->
    <insert id="insertBatch">
        INSERT INTO teacher_time_slot
        (id, teacher_id, weekday, start_time, end_time, status, remark, create_time, update_time, create_by, update_by, deleted)
        VALUES
        <foreach collection="timeSlots" item="timeSlot" separator=",">
            (#{timeSlot.id}, #{timeSlot.teacherId}, #{timeSlot.weekday}, #{timeSlot.startTime}, #{timeSlot.endTime},
             #{timeSlot.status}, #{timeSlot.remark}, #{timeSlot.createTime}, #{timeSlot.updateTime},
             #{timeSlot.createBy}, #{timeSlot.updateBy}, false)
        </foreach>
    </insert>

    <!-- 批量更新教师时间表 -->
    <update id="updateBatchByTeacherId">
        <!-- 先删除原有记录 -->
        UPDATE teacher_time_slot
        SET deleted = true, update_time = CURRENT_TIMESTAMP
        WHERE teacher_id = #{teacherId};

        <!-- 再插入新记录 -->
        <if test="timeSlots != null and timeSlots.size() > 0">
            INSERT INTO teacher_time_slot
            (id, teacher_id, weekday, start_time, end_time, status, remark, create_time, update_time, deleted)
            VALUES
            <foreach collection="timeSlots" item="timeSlot" separator=",">
                (#{timeSlot.id}, #{teacherId}, #{timeSlot.weekday}, #{timeSlot.startTime}, #{timeSlot.endTime},
                 #{timeSlot.status}, #{timeSlot.remark}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false)
            </foreach>
        </if>
    </update>

</mapper>
