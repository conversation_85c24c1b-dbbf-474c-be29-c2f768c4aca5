<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.StudentWordProgressMapper">


    <select id="getByNotDelBook" resultType="org.nonamespace.word.server.domain.StudentWordProgress">
        SELECT swp.*,ti.deleted  FROM words.student_word_progress swp
        left join words.textbook t on swp.textbook_id =t.id
        left join words.textbook_item ti on swp.textbook_item_id  = ti.id
        where
        swp.deleted =false and t.deleted =false and ti.deleted =false and student_id = #{studentId}
        <if test="textbookId != null and textbookId != ''">
            and t.id=#{textbookId}
        </if>

        order by swp.create_time desc
        limit 1
    </select>
</mapper>