<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TeachingGroupMemberMapper">

    <resultMap id="TeacherBasicRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeacherDto$BasicResp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="avatar" property="avatar"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="TeachingGroupRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto$Resp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="leader_id" property="leaderId"/>
        <result column="leader_name" property="leaderName"/>
        <result column="admin_id" property="adminId"/>
        <result column="admin_name" property="adminName"/>
        <result column="member_count" property="memberCount"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询教学组教师列表 -->
    <select id="selectGroupTeachersPage" resultMap="TeacherBasicRespMap">
        SELECT
            tp.teacher_id as id,
            tp.real_name as name,
            tp.nick_name as nickname,
            tp.gender,
            tp.phonenumber as phone,
            '' as email,
            tp.avatar,
            tgm.group_id,
            tg.name as group_name,
            tp.status,
            tp.create_time,
            tp.update_time
        FROM teaching_group_member tgm
        INNER JOIN teacher_profile tp ON tgm.teacher_id = tp.teacher_id AND tp.deleted = false
        INNER JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        WHERE tgm.group_id = #{req.groupId} AND tgm.deleted = false AND tgm.status = 'active'
        <if test="req.name != null and req.name != ''">
            AND (tp.real_name LIKE '%' || #{req.name} || '%' OR tp.nick_name LIKE '%' || #{req.name} || '%')
        </if>
        <if test="req.status != null and req.status != ''">
            AND tp.status = #{req.status}
        </if>
        ORDER BY tgm.join_time DESC
    </select>

    <!-- 查询教学组所有教师 -->
    <select id="selectGroupTeachers" resultMap="TeacherBasicRespMap">
        SELECT
            tp.teacher_id as id,
            tp.real_name as name,
            tp.nick_name as nickname,
            tp.gender,
            tp.phonenumber as phone,
            '' as email,
            tp.avatar,
            tgm.group_id,
            tg.name as group_name,
            tp.status,
            tp.create_time,
            tp.update_time
        FROM teaching_group_member tgm
        INNER JOIN teacher_profile tp ON tgm.teacher_id = tp.teacher_id AND tp.deleted = false
        INNER JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        WHERE tgm.group_id = #{groupId} AND tgm.deleted = false AND tgm.status = 'active'
        ORDER BY tgm.join_time DESC
    </select>

    <!-- 批量添加教师到教学组 -->
    <insert id="insertBatch">
        INSERT INTO teaching_group_member
        (id, group_id, teacher_id, role_type, join_time, status, create_time, update_time, create_by, update_by, deleted)
        VALUES
        <foreach collection="members" item="member" separator=",">
            (#{member.id}, #{member.groupId}, #{member.teacherId}, #{member.roleType}, 
             #{member.joinTime}, #{member.status}, #{member.createTime}, #{member.updateTime}, 
             #{member.createBy}, #{member.updateBy}, false)
        </foreach>
    </insert>

    <!-- 批量移除教师从教学组 -->
    <update id="deleteBatchByGroupIdAndTeacherIds">
        UPDATE teaching_group_member
        SET deleted = true, update_time = CURRENT_TIMESTAMP
        WHERE group_id = #{groupId} AND teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </update>

    <!-- 根据教学组ID删除所有成员 -->
    <update id="deleteByGroupId">
        UPDATE teaching_group_member
        SET deleted = true, update_time = CURRENT_TIMESTAMP
        WHERE group_id = #{groupId}
    </update>

    <!-- 检查教师是否已在其他教学组 -->
    <select id="selectAssignedTeachers" resultType="string">
        SELECT DISTINCT teacher_id 
        FROM teaching_group_member tgm
        INNER JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        WHERE tgm.teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
        AND tgm.deleted = false AND tgm.status = 'active'
    </select>

    <!-- 获取教师的教学组信息 -->
    <select id="selectTeacherGroup" resultMap="TeachingGroupRespMap">
        SELECT 
            tg.id,
            tg.name,
            tg.description,
            tg.leader_id,
            leader.user_name as leader_name,
            tg.admin_id,
            admin.user_name as admin_name,
            tg.member_count,
            tg.status,
            tg.remark,
            tg.create_time,
            tg.update_time
        FROM teaching_group_member tgm
        INNER JOIN teaching_group tg ON tgm.group_id = tg.id AND tg.deleted = false
        LEFT JOIN sys_user leader ON tg.leader_id = leader.user_id AND leader.del_flag = '0'
        LEFT JOIN sys_user admin ON tg.admin_id = admin.user_id AND admin.del_flag = '0'
        WHERE tgm.teacher_id = #{teacherId} AND tgm.deleted = false AND tgm.status = 'active'
        LIMIT 1
    </select>

</mapper>
