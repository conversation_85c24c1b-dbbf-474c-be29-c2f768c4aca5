import { defineComponent, reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getProductListApi,
  getProductDetailApi,
  createProductA<PERSON>,
  updateProductApi,
  deleteProduct<PERSON>pi,
  enableProductApi,
  disableProduct<PERSON><PERSON>,
  type QueryParams,
  type ProductForm,
  type ProductItem
} from '@/api/management/product'

// 类型定义已从API文件导入

export default defineComponent({
  name: 'ProductManagement',
  setup() {
    // 响应式数据
    const loading = ref<boolean>(true)
    const ids = ref<string[]>([])
    const single = ref<boolean>(true)
    const multiple = ref<boolean>(true)
    const showSearch = ref<boolean>(true)
    const total = ref<number>(0)
    const productList = ref<ProductItem[]>([])
    const title = ref<string>('')
    const open = ref<boolean>(false)

    // 查询参数
    const queryParams = reactive<QueryParams>({
      pageNum: 1,
      pageSize: 10
    })

    // 表单数据
    const form = reactive<ProductForm>({
      name: '',
      description: '',
      subject: '',
      courseType: '',
      applicableGrades: [],
      unitPrice: 0,
      quantity: 1,
      hasBonusHours: false,
      bonusHoursQuantity: 0,
      hasMaterialFee: false,
      materialFee: 0,
      originalPrice: 0,
      sellingPrice: 0,
      status: '上架',
      sortOrder: 0,
      remark: ''
    })

    // 表单校验规则
    const rules = {
      name: [
        { required: true, message: '产品名称不能为空', trigger: 'blur' }
      ],
      subject: [
        { required: true, message: '学科不能为空', trigger: 'change' }
      ],
      applicableGrades: [
        { required: true, message: '适用年级不能为空', trigger: 'change' }
      ],
      unitPrice: [
        { required: true, message: '单价不能为空', trigger: 'blur' }
      ],
      quantity: [
        { required: true, message: '数量不能为空', trigger: 'blur' }
      ],
      sellingPrice: [
        { required: true, message: '售价不能为空', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '状态不能为空', trigger: 'change' }
      ]
    }

    // 计算属性
    const unitPriceInYuan = computed({
      get: () => form.unitPrice ? form.unitPrice / 100 : 0,
      set: (value: number) => {
        form.unitPrice = Math.round(value * 100)
        calculateOriginalPrice()
      }
    })

    const materialFeeInYuan = computed({
      get: () => form.materialFee ? form.materialFee / 100 : 0,
      set: (value: number) => {
        form.materialFee = Math.round(value * 100)
        calculateOriginalPrice()
      }
    })

    const originalPriceInYuan = computed({
      get: () => form.originalPrice ? form.originalPrice / 100 : 0,
      set: (value: number) => {
        form.originalPrice = Math.round(value * 100)
      }
    })

    const sellingPriceInYuan = computed({
      get: () => form.sellingPrice ? form.sellingPrice / 100 : 0,
      set: (value: number) => {
        form.sellingPrice = Math.round(value * 100)
      }
    })

    // 方法定义
    /** 查询产品列表 */
    const getList = async (): Promise<void> => {
      try {
        loading.value = true
        const response = await getProductListApi(queryParams)
        debugger
        if (response.data) {
          productList.value = response.data.records || []
          total.value = response.data.total || 0
        }
      } catch (error) {
        console.error('获取产品列表失败:', error)
        ElMessage.error('获取产品列表失败')
      } finally {
        loading.value = false
      }
    }

    /** 搜索按钮操作 */
    const handleQuery = (): void => {
      queryParams.pageNum = 1
      getList()
    }

    /** 重置按钮操作 */
    const resetQuery = (): void => {
      Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        subject: undefined,
        courseType: undefined,
        status: undefined
      })
      handleQuery()
    }

    /** 计算原价 */
    const calculateOriginalPrice = (): void => {
      if (form.unitPrice && form.quantity) {
        const basePrice = form.unitPrice * form.quantity
        const materialFee = form.hasMaterialFee ? form.materialFee : 0
        form.originalPrice = basePrice + materialFee
      } else {
        form.originalPrice = 0
      }
    }

    /** 赠送课时变化处理 */
    const onBonusHoursChange = (value: boolean): void => {
      if (!value) {
        form.bonusHoursQuantity = 0
      }
    }

    /** 教材费变化处理 */
    const onMaterialFeeChange = (value: boolean): void => {
      if (!value) {
        form.materialFee = 0
      }
      calculateOriginalPrice()
    }

    /** 表单重置 */
    const reset = (): void => {
      Object.assign(form, {
        id: undefined,
        name: '',
        description: '',
        subject: '',
        courseType: '',
        applicableGrades: [],
        unitPrice: 0,
        quantity: 1,
        hasBonusHours: false,
        bonusHoursQuantity: 0,
        hasMaterialFee: false,
        materialFee: 0,
        originalPrice: 0,
        sellingPrice: 0,
        status: '上架',
        sortOrder: 0,
        remark: ''
      })
    }

    /** 取消按钮 */
    const cancel = (): void => {
      open.value = false
      reset()
    }

    /** 新增按钮操作 */
    const handleAdd = (): void => {
      reset()
      open.value = true
      title.value = '添加产品'
    }

    /** 修改按钮操作 */
    const handleUpdate = async (row: ProductItem): Promise<void> => {
      reset()
      try {
        const response = await getProductDetailApi(row.id)
        if (response.data) {
          Object.assign(form, response.data)
          open.value = true
          title.value = '修改产品'
        }
      } catch (error) {
        ElMessage.error('获取产品详情失败')
      }
    }

    /** 提交按钮 */
    const submitForm = async (): Promise<void> => {
      try {
        if (form.id) {
          await updateProductApi(form)
          ElMessage.success('修改成功')
        } else {
          await createProductApi(form)
          ElMessage.success('新增成功')
        }
        open.value = false
        getList()
      } catch (error) {
        ElMessage.error(form.id ? '修改失败' : '新增失败')
      }
    }

    /** 删除按钮操作 */
    const handleDelete = async (row: ProductItem): Promise<void> => {
      try {
        await ElMessageBox.confirm('是否确认删除该产品？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteProductApi(row.id)
        ElMessage.success('删除成功')
        getList()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }

    /** 上架按钮操作 */
    const handleEnable = async (row: ProductItem): Promise<void> => {
      try {
        await ElMessageBox.confirm('确认要上架该产品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        await enableProductApi(row.id)
        ElMessage.success('上架成功')
        getList()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('上架失败')
        }
      }
    }

    /** 下架按钮操作 */
    const handleDisable = async (row: ProductItem): Promise<void> => {
      try {
        await ElMessageBox.confirm('确认要下架该产品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        await disableProductApi(row.id)
        ElMessage.success('下架成功')
        getList()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('下架失败')
        }
      }
    }

    // 生命周期
    onMounted(() => {
      getList()
    })

    return {
      // 响应式数据
      loading,
      ids,
      single,
      multiple,
      showSearch,
      total,
      productList,
      title,
      open,
      queryParams,
      form,
      rules,
      
      // 计算属性
      unitPriceInYuan,
      materialFeeInYuan,
      originalPriceInYuan,
      sellingPriceInYuan,
      
      // 方法
      getList,
      handleQuery,
      resetQuery,
      calculateOriginalPrice,
      onBonusHoursChange,
      onMaterialFeeChange,
      reset,
      cancel,
      handleAdd,
      handleUpdate,
      submitForm,
      handleDelete,
      handleEnable,
      handleDisable
    }
  }
})
