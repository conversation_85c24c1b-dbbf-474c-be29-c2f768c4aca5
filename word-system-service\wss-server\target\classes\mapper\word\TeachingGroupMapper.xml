<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TeachingGroupMapper">

    <resultMap id="TeachingGroupRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto$Resp">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="leader_id" property="leaderId"/>
        <result column="leader_name" property="leaderName"/>
        <result column="admin_id" property="adminId"/>
        <result column="admin_name" property="adminName"/>
        <result column="member_count" property="memberCount"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="StatsRespMap" type="org.nonamespace.word.server.dto.management.teachinggroup.TeachingGroupDto$StatsResp">
        <result column="total_groups" property="totalGroups"/>
        <result column="active_groups" property="activeGroups"/>
        <result column="total_teachers" property="totalTeachers"/>
        <result column="unassigned_teachers" property="unassignedTeachers"/>
    </resultMap>

    <!-- 分页查询教学组列表 -->
    <select id="selectTeachingGroupPage" resultMap="TeachingGroupRespMap">
        SELECT
            tg.id,
            tg.name,
            tg.description,
            tg.dept_id,
            dept.dept_name as dept_name,
            tg.leader_id,
            leader.nick_name as leader_name,
            tg.admin_id,
            admin.nick_name as admin_name,
            tg.member_count,
            tg.status,
            tg.remark,
            tg.create_time,
            tg.update_time
        FROM teaching_group tg
        LEFT JOIN sys_dept dept ON tg.dept_id = dept.dept_id AND dept.del_flag = '0'
        LEFT JOIN sys_user leader ON tg.leader_id = leader.user_id::varchar AND leader.del_flag = '0'
        LEFT JOIN sys_user admin ON tg.admin_id = admin.user_id::varchar AND admin.del_flag = '0'
        WHERE tg.deleted = false
        <if test="req.name != null and req.name != ''">
            AND tg.name LIKE '%' || #{req.name} || '%'
        </if>
        <if test="req.status != null and req.status != ''">
            AND tg.status = #{req.status}
        </if>
        <if test="req.leaderId != null and req.leaderId != ''">
            AND tg.leader_id = #{req.leaderId}
        </if>
        <if test="req.adminId != null and req.adminId != ''">
            AND tg.admin_id = #{req.adminId}
        </if>
        ORDER BY tg.create_time DESC
    </select>

    <!-- 根据ID查询教学组详情 -->
    <select id="selectTeachingGroupById" resultMap="TeachingGroupRespMap">
        SELECT
            tg.id,
            tg.name,
            tg.description,
            tg.dept_id,
            dept.dept_name as dept_name,
            tg.leader_id,
            leader.nick_name as leader_name,
            tg.admin_id,
            admin.nick_name as admin_name,
            tg.member_count,
            tg.status,
            tg.remark,
            tg.create_time,
            tg.update_time
        FROM teaching_group tg
        LEFT JOIN sys_dept dept ON tg.dept_id = dept.dept_id AND dept.del_flag = '0'
        LEFT JOIN sys_user leader ON tg.leader_id = leader.user_id::varchar AND leader.del_flag = '0'
        LEFT JOIN sys_user admin ON tg.admin_id = admin.user_id::varchar AND admin.del_flag = '0'
        WHERE tg.id = #{id} AND tg.deleted = false
    </select>

    <!-- 获取教学组统计信息 -->
    <select id="selectTeachingGroupStats" resultMap="StatsRespMap">
        SELECT
            (SELECT COUNT(*) FROM teaching_group WHERE deleted = false) as total_groups,
            (SELECT COUNT(*) FROM teaching_group WHERE deleted = false AND status = 'active') as active_groups,
            (SELECT COUNT(DISTINCT tgm.teacher_id)
             FROM teaching_group_member tgm
             INNER JOIN teaching_group tg ON tgm.group_id = tg.id
             WHERE tgm.deleted = false AND tg.deleted = false AND tgm.status = 'active') as total_teachers,
            (SELECT COUNT(*)
             FROM sys_user u
             INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
             INNER JOIN sys_role r ON ur.role_id = r.role_id
             WHERE u.del_flag = '0' AND r.del_flag = '0' AND r.role_name = '老师'
             AND u.user_id NOT IN (
                 SELECT DISTINCT tgm.teacher_id
                 FROM teaching_group_member tgm
                 INNER JOIN teaching_group tg ON tgm.group_id = tg.id
                 WHERE tgm.deleted = false AND tg.deleted = false AND tgm.status = 'active'
             )) as unassigned_teachers
    </select>

    <!-- 更新教学组成员数量 -->
    <update id="updateMemberCount">
        UPDATE teaching_group
        SET member_count = (
            SELECT COUNT(*)
            FROM teaching_group_member
            WHERE group_id = #{groupId} AND deleted = false AND status = 'active'
        ),
        update_time = CURRENT_TIMESTAMP
        WHERE id = #{groupId}
    </update>

    <!-- 批量删除教学组 -->
    <update id="deleteBatchByIds">
        UPDATE teaching_group
        SET deleted = true, update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
