<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.CourseMapper">


    <!-- 获取明天上课的课程 -->
    <select id="getTomorrowCourseList" resultType="org.nonamespace.word.server.domain.Course">
        with valid_course as (
            select * from course where scheduled_start_time > NOW() and course_status = '待开始'
        )
        select
            vc.id,
            vc.teacher_id as teacherId,
            vc.student_id as studentId,
            vc.type,
            vc.scheduled_start_time as scheduledStartTime,
            vc.subject,
            vc.specification
        from valid_course vc
        where vc.scheduled_start_time::date = (NOW() + INTERVAL '1 day')::date
    </select>
    
    <!-- 获取今天上课前12分钟的数据 -->
    <select id="getToday12MinCourseList" resultType="org.nonamespace.word.server.domain.Course">
        with valid_course as (
            select * from course where scheduled_start_time > NOW() and course_status='待开始'
        )
        select
            vc.id,
            vc.teacher_id as teacherId,
            vc.student_id as studentId,
            vc.type,
            vc.scheduled_start_time as scheduledStartTime,
            vc.subject,
            vc.specification
        from valid_course vc
        where vc.scheduled_start_time &lt;= NOW() + INTERVAL '12 minutes'
        AND vc.scheduled_start_time::date = NOW()::date
    </select>


    <!-- 20点后，实时推送 创建时间为20-24点的，且开课时间大于now，且小于第二天24点的。状态为待开始的。 -->
    <select id="getAfter20ModifyCourseList" resultType="org.nonamespace.word.server.domain.Course">
        with valid_course as (
            select * from course where scheduled_start_time > NOW() and course_status='待开始'
        )
        select
            vc.id,
            vc.teacher_id as teacherId,
            vc.student_id as studentId,
            vc.type,
            vc.scheduled_start_time as scheduledStartTime,
            vc.subject,
            vc.specification
        from valid_course vc
        where vc.scheduled_start_time &gt;= (NOW() - INTERVAL '2 minutes') AND vc.scheduled_start_time &lt; (NOW()::date + INTERVAL '1 day')::timestamp WITH TIME ZONE
        AND vc.create_time &gt;= NOW() - INTERVAL '2 minutes' AND vc.create_time::time &lt;= '23:59:59';
    </select>

</mapper>