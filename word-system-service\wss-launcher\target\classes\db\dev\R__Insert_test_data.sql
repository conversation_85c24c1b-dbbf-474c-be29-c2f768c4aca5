-- 开发环境测试数据脚本
-- 此脚本仅在开发环境执行，用于插入测试数据
-- 可重复执行，每次启动时都会执行

-- ============================================================================
-- 测试数据说明
-- ============================================================================
-- 此脚本包含开发环境所需的测试数据
-- 包括：测试用户、测试课程、测试数据等
-- 注意：此脚本仅在开发环境执行，生产环境不会加载

-- ============================================================================
-- 清理现有测试数据（可选）
-- ============================================================================
-- 如果需要重置测试数据，可以取消注释以下语句
-- DELETE FROM test_table WHERE created_by = 'flyway_test_data';

-- ============================================================================
-- 插入测试数据
-- ============================================================================

-- 示例：插入测试用户数据
-- INSERT INTO sys_user (
--     user_id, user_name, nick_name, email, phonenumber, 
--     sex, avatar, password, status, del_flag, 
--     create_by, create_time, remark
-- ) VALUES (
--     'test_user_001', 'testuser1', '测试用户1', '<EMAIL>', '13800138001',
--     '0', '', '$2a$10$7JB720yubVSOfvam/l0myO4oiMEab90N9QuuSX4EqHKIDoxVu.upq', -- 密码: admin123
--     '0', '0', 
--     'flyway_test_data', NOW(), '开发环境测试用户'
-- ) ON CONFLICT (user_id) DO NOTHING;

-- 示例：插入测试角色数据
-- INSERT INTO sys_role (
--     role_id, role_name, role_key, role_sort, data_scope,
--     menu_check_strictly, dept_check_strictly, status, del_flag,
--     create_by, create_time, remark
-- ) VALUES (
--     'test_role_001', '测试角色', 'test_role', 1, '1',
--     '1', '1', '0', '0',
--     'flyway_test_data', NOW(), '开发环境测试角色'
-- ) ON CONFLICT (role_id) DO NOTHING;

-- 示例：插入测试课程数据
-- INSERT INTO course (
--     id, course_name, subject, specification, nature,
--     grade, status, create_time, create_by
-- ) VALUES (
--     'test_course_001', '测试英语单词课', '英语', '单词课', '试听课',
--     '小学三年级', '待开始', NOW(), 'flyway_test_data'
-- ) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 测试数据验证
-- ============================================================================
-- 验证测试数据是否插入成功
-- DO $$
-- DECLARE
--     test_user_count INTEGER;
-- BEGIN
--     SELECT COUNT(*) INTO test_user_count 
--     FROM sys_user 
--     WHERE create_by = 'flyway_test_data';
--     
--     IF test_user_count > 0 THEN
--         RAISE NOTICE '开发环境测试数据插入成功，共插入 % 个测试用户', test_user_count;
--     ELSE
--         RAISE NOTICE '未插入测试数据或测试数据已存在';
--     END IF;
-- END $$;

-- ============================================================================
-- 开发环境特定配置
-- ============================================================================
-- 开发环境可能需要的特殊配置

-- 示例：设置开发环境特定的系统参数
-- INSERT INTO sys_config (
--     config_id, config_name, config_key, config_value, config_type,
--     create_by, create_time, remark
-- ) VALUES (
--     'dev_config_001', '开发环境标识', 'system.dev.mode', 'true', 'Y',
--     'flyway_test_data', NOW(), '标识当前为开发环境'
-- ) ON CONFLICT (config_key) DO UPDATE SET 
--     config_value = EXCLUDED.config_value,
--     update_time = NOW();

-- ============================================================================
-- 注意事项
-- ============================================================================
-- 1. 此脚本仅在开发环境执行
-- 2. 使用 ON CONFLICT DO NOTHING 避免重复插入
-- 3. 所有测试数据都标记 create_by = 'flyway_test_data' 便于识别和清理
-- 4. 可以根据需要添加更多测试数据
-- 5. 生产环境部署时此脚本不会执行
