# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
#        driverClassName: org.postgresql.Driver
        driverClassName: com.p6spy.engine.spy.P6SpyDriver
        druid:
            # 主库数据源
            master:
                url: *******************************************************************************************************************************************************************************************************************
                username: main
                password: 'Tongbu121!'
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 100
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT version()
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

    data:
        # redis 配置
        redis:
            # 地址
            host: word.121tongbu.com
            # 端口，默认为6379
            port: 19736
            # 数据库索引
            database: 0
            # 密码
            password: 'Redis123!'
            # 连接超时时间
            timeout: 10s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
    ai:
        openai:
            # 聊天模型
            chat:
                options:
                    model: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
            api-key: sk-tnesojkqpqozrtzfcxfkvucksdfgzmgysehqmrwbaoblwedi
            base-url: https://api.siliconflow.cn/
            temperature: ${OPENAI_TEMPERATURE:0.7}
            top-p: ${OPENAI_TOP_P:1.0}
            max-tokens: ${OPENAI_MAX_TOKENS:2000}
    flyway:
        enabled: false
# 阿里云OSS配置
aliyun:
    oss:
        endpoint: oss-cn-shenzhen.aliyuncs.com
        accessKeyId: LTAI5tB4PDg4LaNBgbNVpW9g
        accessKeySecret: ******************************
        bucketName: tongbu-words-dev
        urlPrefix: https://tongbu-words-dev.oss-cn-shenzhen.aliyuncs.com/

nebius:
    base-url: https://api.studio.nebius.com
    api-key: eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMDI3NjM1MTUyODMwNDIwMDc1MyIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0MDg4MiwidXVpZCI6ImRjNzJlOGFmLTdkNDctNGUxNy04MjMyLTAwYmU1Yzc4Mjc4YyIsIm5hbWUiOiJ3b3JkcyIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEwOjQxOjIyKzAwMDAifQ.T3OWY8SA_6G2DNzHaD4E2iuioBi7s56pZX7GyGQwusk
    model: Qwen/Qwen3-32B
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890

xai:
    base-url: https://api.studio.nebius.com/v1/chat/completions
    api-key: eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMDI3NjM1MTUyODMwNDIwMDc1MyIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0MDg4MiwidXVpZCI6ImRjNzJlOGFmLTdkNDctNGUxNy04MjMyLTAwYmU1Yzc4Mjc4YyIsIm5hbWUiOiJ3b3JkcyIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEwOjQxOjIyKzAwMDAifQ.T3OWY8SA_6G2DNzHaD4E2iuioBi7s56pZX7GyGQwusk
    model: deepseek-ai/DeepSeek-V3-0324
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890

word:
    audio:
        save-path: F:\\voice
        base-url: https://dict.youdao.com/dictvoice
    enrich:
        job:
            enabled: false
        words-model: nebuis   # grok / siliconflow / nebuis，单词文本补全，默认为grok

volcengine:
    base-url: https://openspeech.bytedance.com
    speech:
        appid: 7167433942
        cluster: volcano_tts
        secretkey: Rgom2zgcp1VeySqr7oAefF8x4Et-MQTe
        access-token: lMA59dpe1S4RfAS2hJrm3Xt_xZR7L00_
        voice-type:
            us: zh_female_shuangkuaisisi_moon_bigtts
            uk: en_male_smith_mars_bigtts

clawcloud:
    url: https://iqzphfbrhgsd.ap-northeast-1.clawcloudrun.com/tts
    voice:
        us: en-US-AvaMultilingualNeural
        uk: en-GB-RyanNeural

wx:
    mp:
        job:
            enabled: false
        configs:
            - appId: wx81bac75532ac7de9
              secret: f5d423eba860626b20517882c8672e6c
              learningReportTemplateId: DQncyQJQ-KSmzn31smmI-63VT3seZwXf2KqAqx3k_iY
              startCourseTemplateId: LwucLOiZV2KRUU4UwhVxtDWIHYGUyqhO0cDKAdxESZE
        redirectUri: http://wxmapp.shamee.top/#/pages/bind/index
        learningReportDetailUrl: http://wxmapp.shamee.top/#/pages/course/report?id=%s
        reviewReportDetailUrl: http://wxmapp.shamee.top/#/pages/course/review-report?id=%s

course:
    remind:
        job:
            enabled: false

# 通联支付配置
allinpay:
    # 接口地址
    base-url: https://vsp.allinpay.com
    cusid: '990440148166000'
    appid: '00000003'
    private-key: 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDrjRCrziqLp6P/a2xMxm1G+JGOersAPq8JqSuVdXfkDOhDGYAnFV+bb+ypJCLqAydfeamt24nTN4SX2Z1mcmlfkYRugBlKIu7T/uXQKt7Hx/IwLv63TPfUwtzqRzxJ50KL6fniGZePL60RpBC+3cQ5z4LfUDZv40zm+k5VFSHwEg7+zXXFhcuzE7ZDxaCqmTBgQp6LAQ5nnZqM2ya7B6ERHuMmqMPOYTTB6giLlQmUrEqVXf0d0DfxMyXuVrJmX5F0YuMUo/AR/6rndaep3p58/t6R+Q7BplM76CfRKNHh6BZb40T3TtSz2wIpcpMeflup+TXp0FSIzz4IlOG/vZMHAgMBAAECggEASDtk7lp1f4fz6YKGnnCG2CmncRq/t46BXKQM4nM4Ra2Tl+f8/X83Z1IGL8QOFNbMvyFu4AqflC7FMU+hTeYt3Amio8QSfUKDHG1hCdnroTX2VcJDh/BJW2w6FHmtyjfpl0AzyZrxeA41s2AtZmpZHyU8S8CsQNliHr6JQNwPIS/B3/4CWlzOMVa9ONSTOWPKZXExzlB9AmypkU9lm4E6GlCbDb8tve3ikF34JD+xpHQ/DclKc6KJAmnyxeaOku5JAR621eWk8bjYVwQRvKyt/mqwI245//nvHj3f/tp1FsWxaYSiGTFU+G7cxF0OvUI4P6+1ZYNRiAdyCoKSDFgLqQKBgQD2S8K03N/J+UjJvLRUhLJEz4n+aDXzgnGgKEJl6NdpxP2fhlFBwOnz4tPJuuz8RWhOtu8E4ZLW7+D8ro+6L+GSTwMJwGr1HroSCHriW/jDWpvXsdP8l4S5n2tWca2GPs6dAkdM+brpGujelcaCL1xKiEpY+YheYFzwYWkI3MPN+wKBgQD01O1ZbnYhIujJtPDDapye8SRZeHRDtaZQ6UqKpYsBnJNUiceME4ItMAtSfip0XCUzrLL2l9CjJIRl9h7h+nv1mZN2+25a/eXx8DwR6krcj016vauoUfOm7udog/IEMsBN2+uwevroH3atdxb9TOzT0t9K7ihOd7d71GJY0VG9ZQKBgQDvhSHnLd3c17c3qXJdt2Z+kK6MYOAG3laOAFkCLnw++joEB2XCYk+ixHZeepC5jO4xjqnHv29D46iTU1enI6Vk2b4En8Hke2tM2j5XIEY+BUIFc4TK4rdKpYSHXqn4IeBgxLR+YWaqB2dr/5A7Tr+cJArZmGJMFb6Mbs7stXUePQKBgQDQnfqcoK4RsDBRZ1db/CT2nh1OC4BuDYNpIFA+8a4Psn4ro7cf2aOro2uSYCUGv1mHvZlE27mQudYDF4PbJFepv7HSyY2QBRaBYCWfUld1trnbY3C4XRRTYvBVs1BuWVxZO5KJ71CKp/y09GvnbDvEhsNzkplPu1ziK+edLMcMrQKBgHGUpj9HVgU6RBZlmMK+73lSWuBZE6HV8dpmBx5jw/SAtKP9BLHJxS3mx3/H/3lHv13KtkS2zAbKhg2G5EkTvrNp3xph15sX5GQv39ESBef0CqUDCDEXOEk86XPGUGy8wpVqno9IVY+gV54MmOTvM1j607B8YWGWQ7mkKcpYk2sH'
    public-key: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB'
    # 默认通知地址（可选）
    notify-url: https://your-domain.com/allinpay/notify
    # 默认前端跳转地址（可选）
    front-url: https://your-domain.com/pay/success
    # 订单有效时间，单位分钟。默认5分钟
    valid-time: 5

# e签宝配置
esign:
    # 基础URL
    base-url: https://smlopenapi.esign.cn
    # 应用ID
    app-id: **********
    # 应用密钥
    app-secret: 457dc1847426e3b7fd7b8f9ccd3bd3e2
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    # 读取超时时间（毫秒）
    read-timeout: 60000
    # 是否启用
    enabled: true
    doc-template-id: 3706ab4b0c864f6b99c70bd289585236
    sdk-version: Esign-Sdk-Core1.0
