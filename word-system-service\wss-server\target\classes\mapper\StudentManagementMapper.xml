<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.StudentManagementMapper">

    <!-- 学生基础信息结果映射 -->
    <resultMap id="StudentBasicRespMap" type="org.nonamespace.word.server.dto.management.student.StudentDto$BasicResp">
        <id property="id" column="student_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="gender" column="gender"/>
        <result property="grade" column="grade"/>
        <result property="school" column="school"/>
        <result property="className" column="class_name"/>
        <result property="parentName" column="parent_name"/>
        <result property="parentPhone" column="parent_phone"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="totalHours" column="total_hours" javaType="java.math.BigDecimal"/>
        <result property="consumedHours" column="consumed_hours" javaType="java.math.BigDecimal"/>
        <result property="remainingHours" column="remaining_hours" javaType="java.math.BigDecimal"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 学生详细信息结果映射 -->
    <resultMap id="StudentDetailRespMap" type="org.nonamespace.word.server.dto.management.student.StudentDto$DetailResp">
        <id property="id" column="student_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="gender" column="gender"/>
        <result property="grade" column="grade"/>
        <result property="school" column="school"/>
        <result property="className" column="class_name"/>
        <result property="parentName" column="parent_name"/>
        <result property="parentPhone" column="parent_phone"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="totalHours" column="total_hours" javaType="java.math.BigDecimal"/>
        <result property="consumedHours" column="consumed_hours" javaType="java.math.BigDecimal"/>
        <result property="remainingHours" column="remaining_hours" javaType="java.math.BigDecimal"/>
        <result property="learningGoals" column="learning_goals"/>
        <result property="remarks" column="remarks"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 分页查询学生列表 -->
    <select id="selectStudentPage" resultMap="StudentBasicRespMap">
        SELECT
            distinct
            use.student_id as student_id,
            use.name as name,
            use.phone as phone,
            use.gender as gender,
            use.grade,
            use.school,
            use.class_name,
            use.parent_name,
            use.parent_phone,
            CAST(COALESCE(course_hours_summary.total_hours, 0.00) AS DECIMAL(10,2)) as total_hours,
            CAST(COALESCE(course_hours_summary.consumed_hours, 0.00) AS DECIMAL(10,2)) as consumed_hours,
            CAST(COALESCE(course_hours_summary.remaining_hours, 0.00) AS DECIMAL(10,2)) as remaining_hours,
            use.status as status,
            use.create_time as create_time,
            use.update_time as update_time
        FROM user_student_ext use
        LEFT JOIN teacher_student_relation tsr ON use.student_id = tsr.student_id AND tsr.deleted = false AND tsr.status = 'active'
        LEFT JOIN (
            SELECT
                student_id,
                SUM(total_hours) as total_hours,
                SUM(consumed_total_hours) as consumed_hours,
                SUM(remaining_hours) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active'
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.deleted = false

        <if test="req != null and req.keyword != null and req.keyword != ''">
            AND (
                    use.name LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
                    OR
                    use.phone LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
                )
        </if>

        <if test="req != null and req.name != null and req.name != ''">
            AND use.name LIKE '%'||#{req.name,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.phone != null and req.phone != ''">
            AND use.phone LIKE '%'||#{req.phone,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.grade != null and req.grade != ''">
            AND use.grade = #{req.grade,jdbcType=VARCHAR}
        </if>
        <if test="req != null and req.school != null and req.school != ''">
            AND use.school LIKE '%'||#{req.school,jdbcType=VARCHAR}||'%'
        </if>
        <if test="teacherIds != null and teacherIds.size() > 0">
            AND tsr.teacher_id in
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
        </if>
        <if test="req != null and req.status != null and req.status != ''">
            AND use.status = #{req.status,jdbcType=VARCHAR}
        </if>
        ORDER BY use.create_time DESC
    </select>

    <!-- 分页查询学生列表的COUNT查询 -->
    <select id="selectStudentPageCount" resultType="long">
        SELECT COUNT(DISTINCT use.student_id)
        FROM user_student_ext use
        LEFT JOIN teacher_student_relation tsr ON use.student_id = tsr.student_id AND tsr.deleted = false AND tsr.status = 'active'
        WHERE use.deleted = false
        <if test="req != null and req.keyword != null and req.keyword != ''">
            AND (
            use.name LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
            OR
            use.phone LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
            )
        </if>
        <if test="req != null and req.name != null and req.name != ''">
            AND use.name LIKE '%'||#{req.name,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.phone != null and req.phone != ''">
            AND use.phone LIKE '%'||#{req.phone,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.grade != null and req.grade != ''">
            AND use.grade = #{req.grade,jdbcType=VARCHAR}
        </if>
        <if test="req != null and req.school != null and req.school != ''">
            AND use.school LIKE '%'||#{req.school,jdbcType=VARCHAR}||'%'
        </if>
        <if test="teacherIds != null and teacherIds.size() > 0">
            AND tsr.teacher_id in
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
        </if>
        <if test="req != null and req.status != null and req.status != ''">
            AND use.status = #{req.status,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询学生列表（不分页，用于手动分页） -->
    <select id="selectStudentPageList" resultMap="StudentBasicRespMap">
        SELECT
            distinct
            use.student_id as student_id,
            use.name as name,
            use.phone as phone,
            use.gender as gender,
            use.grade,
            use.school,
            use.class_name,
            use.parent_name,
            use.parent_phone,
            CAST(COALESCE(course_hours_summary.total_hours, 0.00) AS DECIMAL(10,2)) as total_hours,
            CAST(COALESCE(course_hours_summary.consumed_hours, 0.00) AS DECIMAL(10,2)) as consumed_hours,
            CAST(COALESCE(course_hours_summary.remaining_hours, 0.00) AS DECIMAL(10,2)) as remaining_hours,
            use.status as status,
            use.create_time as create_time,
            use.update_time as update_time
        FROM user_student_ext use
        LEFT JOIN teacher_student_relation tsr ON use.student_id = tsr.student_id AND tsr.deleted = false AND tsr.status = 'active'
        LEFT JOIN (
            SELECT
                student_id,
                SUM(total_hours) as total_hours,
                SUM(consumed_total_hours) as consumed_hours,
                SUM(remaining_hours) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active'
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.deleted = false

        <if test="req != null and req.keyword != null and req.keyword != ''">
            AND (
                    use.name LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
                    OR
                    use.phone LIKE '%'||#{req.keyword,jdbcType=VARCHAR}||'%'
                )
        </if>

        <if test="req != null and req.name != null and req.name != ''">
            AND use.name LIKE '%'||#{req.name,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.phone != null and req.phone != ''">
            AND use.phone LIKE '%'||#{req.phone,jdbcType=VARCHAR}||'%'
        </if>
        <if test="req != null and req.grade != null and req.grade != ''">
            AND use.grade = #{req.grade,jdbcType=VARCHAR}
        </if>
        <if test="req != null and req.school != null and req.school != ''">
            AND use.school LIKE '%'||#{req.school,jdbcType=VARCHAR}||'%'
        </if>
        <if test="teacherIds != null and teacherIds.size() > 0">
            AND tsr.teacher_id in
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
        </if>
        <if test="req != null and req.status != null and req.status != ''">
            AND use.status = #{req.status,jdbcType=VARCHAR}
        </if>
        ORDER BY use.create_time DESC
        LIMIT #{req.pageSize} OFFSET #{offset}
    </select>

    <!-- 查询学生详细信息 -->
    <select id="selectStudentDetail" resultMap="StudentDetailRespMap">
        SELECT
            distinct
            use.student_id as student_id,
            use.name as name,
            use.phone as phone,
            use.gender as gender,
            use.grade,
            use.school,
            use.class_name,
            use.parent_name,
            use.parent_phone,
            COALESCE(course_hours_summary.total_hours, 0.00) as total_hours,
            COALESCE(course_hours_summary.consumed_hours, 0.00) as consumed_hours,
            COALESCE(course_hours_summary.remaining_hours, 0.00) as remaining_hours,
            use.learning_goals,
            use.remarks,
            use.status,
            use.create_time as create_time,
            use.update_time as update_time
        FROM user_student_ext use
        LEFT JOIN teacher_student_relation tsr ON use.student_id = tsr.student_id AND tsr.deleted = false AND tsr.status = 'active'
        LEFT JOIN (
            SELECT
                student_id,
                COALESCE(SUM(total_hours), 0.00) as total_hours,
                COALESCE(SUM(consumed_total_hours), 0.00) as consumed_hours,
                COALESCE(SUM(remaining_hours), 0.00) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active'
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.student_id = #{studentId} AND use.deleted = false
    </select>

    <!-- 根据教师ID查询学生列表 -->
    <select id="selectStudentsByTeacher" resultMap="StudentBasicRespMap">
        SELECT
            distinct
            use.student_id as student_id,
            use.name as name,
            use.phone as phone,
            use.gender as gender,
            use.grade,
            use.school,
            use.class_name,
            use.parent_name,
            use.parent_phone,
            COALESCE(course_hours_summary.total_hours, 0.00) as total_hours,
            COALESCE(course_hours_summary.consumed_hours, 0.00) as consumed_hours,
            COALESCE(course_hours_summary.remaining_hours, 0.00) as remaining_hours,
            use.status,
            use.create_time as create_time,
            use.update_time as update_time
        FROM user_student_ext use
        INNER JOIN teacher_student_relation tsr ON use.student_id = tsr.student_id AND tsr.deleted = false AND tsr.status = 'active'
        LEFT JOIN (
            SELECT
                student_id,
                COALESCE(SUM(total_hours), 0.00) as total_hours,
                COALESCE(SUM(consumed_total_hours), 0.00) as consumed_hours,
                COALESCE(SUM(remaining_hours), 0.00) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active'
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.deleted = false AND tsr.teacher_id = #{teacherId}
        ORDER BY use.create_time DESC
    </select>

    <!-- 查询可分配的学生列表 -->
    <select id="selectAvailableStudents" resultType="org.nonamespace.word.server.dto.management.student.StudentDto$AvailableResp">
        SELECT
            use.student_id as id,
            use.name as name,
            use.phone as phone,
            use.grade,
            use.school,
            COALESCE(course_hours_summary.remaining_hours, 0.00) as remainingHours,
            use.status
        FROM user_student_ext use
        LEFT JOIN (
            SELECT
                student_id,
                COALESCE(SUM(remaining_hours), 0.00) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active'
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.deleted = false
        AND NOT EXISTS (
            SELECT 1 FROM teacher_student_relation tsr
            WHERE tsr.student_id = use.student_id
            AND tsr.teacher_id = #{teacherId}
            AND tsr.deleted = false
            AND tsr.status = 'active'
        )
        AND use.status = 'active'
        ORDER BY use.create_time DESC
    </select>

    <!-- 查询学生统计信息 -->
    <select id="selectStudentStats" resultType="org.nonamespace.word.server.dto.management.student.StudentDto$StatsResp">
        SELECT
            COUNT(*) as totalStudents,
            COUNT(CASE WHEN use.status = 'active' THEN 1 END) as activeStudents,
            COUNT(CASE WHEN use.status = 'inactive' THEN 1 END) as inactiveStudents,
            COUNT(CASE WHEN use.status = 'graduated' THEN 1 END) as graduatedStudents,
            COUNT(CASE WHEN use.create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as newStudentsThisMonth
        FROM user_student_ext use
        WHERE use.deleted = false
    </select>

    <!-- 查询学生课程统计 -->
    <select id="selectStudentCourseStats" resultType="org.nonamespace.word.server.dto.management.student.StudentDto$CourseStatsResp">
        SELECT
            #{studentId} as studentId,
            use.name as studentName,
            COALESCE(cs.total_courses, 0) as totalCourses,
            COALESCE(cs.completed_courses, 0) as completedCourses,
            COALESCE(cs.scheduled_courses, 0) as scheduledCourses,
            COALESCE(cs.cancelled_courses, 0) as cancelledCourses,
            COALESCE(course_hours_summary.total_hours, 0.00) as totalHours,
            COALESCE(course_hours_summary.consumed_hours, 0.00) as consumedHours,
            COALESCE(course_hours_summary.remaining_hours, 0.00) as remainingHours
        FROM user_student_ext use
        LEFT JOIN (
            SELECT
                student_id,
                COUNT(*) as total_courses,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_courses,
                COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_courses,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_courses
            FROM course
            WHERE student_id = #{studentId}
            GROUP BY student_id
        ) cs ON use.student_id = cs.student_id
        LEFT JOIN (
            SELECT
                student_id,
                COALESCE(SUM(total_hours), 0.00) as total_hours,
                COALESCE(SUM(consumed_total_hours), 0.00) as consumed_hours,
                COALESCE(SUM(remaining_hours), 0.00) as remaining_hours
            FROM student_course_hours
            WHERE deleted = false AND status = 'active' AND student_id = #{studentId}
            GROUP BY student_id
        ) course_hours_summary ON use.student_id = course_hours_summary.student_id
        WHERE use.student_id = #{studentId} AND use.deleted = false
    </select>

    <!-- 查询学生最近课程 -->
    <select id="selectStudentRecentCourses" resultType="org.nonamespace.word.server.dto.management.student.StudentDto$RecentCourseResp">
        SELECT
            c.id,
            COALESCE(tp.nick_name, tp.real_name) as teacherName,
            c.subject,
            c.start_time as startTime,
            c.end_time as endTime,
            c.status,
            c.remarks,
            c.create_time as createTime
        FROM course c
        LEFT JOIN teacher_profile tp ON c.teacher_id = tp.teacher_id AND tp.deleted = false
        WHERE c.student_id = #{studentId}
        ORDER BY c.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询学生课表 -->
    <select id="selectStudentSchedule" resultType="org.nonamespace.word.server.dto.management.student.StudentDto$ScheduleResp">
        SELECT
            c.id,
            c.student_id as studentId,
            c.teacher_id as teacherId,
            COALESCE(tp.nick_name, tp.real_name) as teacherName,
            c.subject,
            c.type as courseType,
            c.start_time as startTime,
            c.end_time as endTime,
            c.status,
            c.remarks
        FROM course c
        LEFT JOIN teacher_profile tp ON c.teacher_id = tp.teacher_id AND tp.deleted = false
        WHERE c.student_id = #{studentId}
        <if test="startDate != null and startDate != ''">
            AND DATE(c.start_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(c.end_time) &lt;= #{endDate}
        </if>
        ORDER BY c.start_time ASC
    </select>

    <!-- 创建学生信息 -->
    <insert id="insertStudent">
        INSERT INTO user_student_ext (
            student_id, name, phone, gender, grade, school, class_name,
            parent_name, parent_phone, learning_goals, remarks, status,
            deleted, create_time, update_time, create_by
        ) VALUES (
            #{student.studentId}, #{student.name}, #{student.phone}, #{student.gender},
            #{student.grade}, #{student.school}, #{student.className},
            #{student.parentName}, #{student.parentPhone}, #{student.learningGoals},
            #{student.remarks}, #{student.status}, false, NOW(), NOW(), 'system'
        )
    </insert>

    <!-- 更新学生信息 -->
    <update id="updateStudent">
        UPDATE user_student_ext SET
            name = #{student.name},
            phone = #{student.phone},
            gender = #{student.gender},
            grade = #{student.grade},
            school = #{student.school},
            class_name = #{student.className},
            parent_name = #{student.parentName},
            parent_phone = #{student.parentPhone},
            learning_goals = #{student.learningGoals},
            remarks = #{student.remarks},
            status = #{student.status},
            update_time = NOW(),
            update_by = 'system'
        WHERE student_id = #{student.studentId} AND deleted = false
    </update>

    <!-- 删除学生信息（软删除） -->
    <update id="deleteStudent">
        UPDATE user_student_ext SET
            deleted = true,
            update_time = NOW(),
            update_by = 'system'
        WHERE student_id = #{studentId}
    </update>

    <!-- 批量删除学生信息（软删除） -->
    <update id="deleteStudents">
        UPDATE user_student_ext SET
            deleted = true,
            update_time = NOW(),
            update_by = 'system'
        WHERE student_id IN
        <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </update>

</mapper>
