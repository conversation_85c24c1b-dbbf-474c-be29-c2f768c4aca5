<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.TextbookItemMapper">
    
    <resultMap type="TextbookItem" id="TextbookItemResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="nodeType"    column="node_type"    />
        <result property="pid"    column="pid"    />
        <result property="wordId"    column="word_id"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="displayOrder"    column="display_order"    />
        <result property="deleted"    column="deleted"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectTextbookItemVo">
        select id, textbook_id, node_type, pid, word_id, video_url, display_order, deleted, created_at, updated_at from textbook_item
    </sql>

    <select id="selectTextbookItemList" parameterType="TextbookItem" resultMap="TextbookItemResult">
        <include refid="selectTextbookItemVo"/>
        <where>  
            <if test="textbookId != null  and textbookId != ''"> and textbook_id = #{textbookId}</if>
            <if test="nodeType != null "> and node_type = #{nodeType}</if>
            <if test="pid != null  and pid != ''"> and pid = #{pid}</if>
            <if test="wordId != null  and wordId != ''"> and word_id = #{wordId}</if>
            <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
            <if test="displayOrder != null "> and display_order = #{displayOrder}</if>
            <if test="deleted != null  and deleted != ''"> and deleted = #{deleted}</if>
            <if test="createdAt != null  and createdAt != ''"> and created_at = #{createdAt}</if>
            <if test="updatedAt != null  and updatedAt != ''"> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectTextbookItemById" parameterType="String" resultMap="TextbookItemResult">
    </select>
    <select id="getTreeItemList" resultType="org.nonamespace.word.server.dto.TextbookItemTreeoDto">
        with student_progress as(
        select distinct on(textbook_item_id)textbook_item_id ,status from student_word_progress
        where textbook_id = #{textbookId} and student_id=#{studentId}
        order by textbook_item_id,create_time desc
        )
        select t."type" as book_type,ti.*,w.word,swp.textbook_item_id as student_word_item_id,swp.status from textbook_item ti
        inner join textbook t on t.id = ti.textbook_id
        left join word w on ti.word_id = w.id
        left join student_progress swp on ti.id = swp.textbook_item_id
        where t.id =#{textbookId} and t.deleted =false and ti.deleted=false
        order by ti.display_order
    </select>
    <select id="getTreeItemListByUnitId" resultType="org.nonamespace.word.server.dto.TextbookItemTreeoDto">
        with student_progress as(
        select distinct on(textbook_item_id)textbook_item_id ,status from student_word_progress
        where student_id=#{studentId}
        order by textbook_item_id,create_time desc
        )
        select t."type" as book_type,ti.*,w.word,swp.textbook_item_id as student_word_item_id,swp.status from textbook_item ti
        inner join textbook t on t.id = ti.textbook_id
        left join word w on ti.word_id = w.id
        left join student_progress swp on ti.id = swp.textbook_item_id
        where ti.pid =#{unitId} and t.deleted =false and ti.deleted=false
        order by ti.display_order
    </select>

    <insert id="insertTextbookItem" parameterType="TextbookItem">
        insert into textbook_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null and textbookId != ''">textbook_id,</if>
            <if test="nodeType != null">node_type,</if>
            <if test="pid != null">pid,</if>
            <if test="wordId != null and wordId != ''">word_id,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="displayOrder != null">display_order,</if>
            <if test="deleted != null">deleted,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null and textbookId != ''">#{textbookId},</if>
            <if test="nodeType != null">#{nodeType},</if>
            <if test="pid != null">#{pid},</if>
            <if test="wordId != null and wordId != ''">#{wordId},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateTextbookItem" parameterType="TextbookItem">
        update textbook_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null and textbookId != ''">textbook_id = #{textbookId},</if>
            <if test="nodeType != null">node_type = #{nodeType},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="wordId != null and wordId != ''">word_id = #{wordId},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTextbookItemById" parameterType="String">
        delete from textbook_item where id = #{id}
    </delete>

    <delete id="deleteTextbookItemByIds" parameterType="String">
        delete from textbook_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>