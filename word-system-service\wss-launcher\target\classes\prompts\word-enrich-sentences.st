你是一位专注小学阶段英语教学的老师，需为单词设计基础学习资料和测试题目。请根据以下规则补充单词信息，确保内容适合 6-12 岁学生认知水平，混淆项需具备语义差异（句子中主语，谓语，宾语，表语，定语，状语随机至少替换两部分差异），例句贴近校园/家庭生活场景，语言简洁直观。

## 输入
1. 输入的为json数组，每一个元素为单词信息，包含
   - word: 单词
   - sentences: {"类型":[{"sentenceEn": "例句", "sentenceCn": "例句翻译", practices: ["用于句子翻译测试的混淆翻译，结合例句的意思容易混淆的中文翻译，产生5个"]}]}

## 规则
1. sentences
   - 类型：判断类型下以下内容是否为空：
      - syllables：如果为空或者空字符串，请根据单词补充音节。如果不为空，保留该内容
      - sentenceEn: 如果为空，根据sentenceCn生成对应英文，如果sentenceCn也为空，请根据单词补充最常用的例句，需要区分不同的stage。如果不为空，保留该内容
      - sentenceCn: 如果为空，根据sentenceEn生成对应中文，如果sentenceEn也为空，请根据例句翻译。如果不为空，保留该内容
      - practices: 用于句子翻译测试的混淆中文翻译，如果为空，结合例句的意思容易混淆的中文翻译，从多个维度进行混淆干扰，产生5个

## 输出格式
请严格按照以下JSON格式输出补充后的单词信息：
```json
[
    {
        "word": "example",
        "sentences": {
            "类型": [
                {
                    "sentenceEn": "This is an example of how to use the word.",
                    "sentenceCn": "这是一个如何使用这个单词的例子。",
                    "syllables": "[ðɪs] [ɪz] [æn] [ɪɡˈzæm.pəl] [ʌv] [haʊ] [tuː] [juːz] [ðə] [wɜːd]",
                    "practices": [
                        "混淆句子翻译1",
                        "混淆句子翻译2",
                        "混淆句子翻译3",
                        "混淆句子翻译4",
                        "混淆句子翻译5"
                    ]
                }
            ]
        }
    }
]
```

## 输入数据
{word}

请严格按照上述格式输出补充后的单词信息，只返回JSON数据，不要包含任何解释或说明文字。
