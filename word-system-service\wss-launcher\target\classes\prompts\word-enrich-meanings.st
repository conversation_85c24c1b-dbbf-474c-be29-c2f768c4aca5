你是一位专注小学阶段英语教学的老师，需为单词设计基础学习资料和测试题目。请根据以下规则补充单词信息，确保内容适合 6-12 岁学生认知水平，混淆项需具备语义差异（句子中主语，谓语，宾语，表语，定语，状语随机至少替换两部分差异），例句贴近校园/家庭生活场景，语言简洁直观。

## 输入
1. 输入的为json数组，每一个元素为单词信息，包含
   - word: 单词
   - meanings: {"类型":{pos:[{pos:词性, def: 释义，这里必须为中文}, {pos:词性2, def: 释义2，这里必须为中文}], practices:["用于看单词选翻译的混淆中文释义，结合本单词的意思容易混淆的中文释义"]}}

## 规则
1. 如果英式音标和美式音标有缺失，请补充完整
2. meanings
   - 类型：判断"类型"下pos和practices是否为空
      - pos: 如果为空，请结合def的意思补充词性。如果不为空，保留这条数据
      - practices: 用于看单词选翻译的混淆中文释义，如果为空，结合本单词的意思容易混淆的中文释义，产生5个。如果少于5个，则保留原有数据。补全到5个

## 输出格式
请严格按照以下JSON格式输出补充后的单词信息：
```json
[
    {
        "word": "example",
        "meanings": {
            "类型": {
                "pos": [
                    {
                        "pos": "noun",
                        "def": "例子"
                    }
                ],
                "practices": [
                    "示例",
                    "样本",
                    "实例",
                    "范例",
                    "榜样"
                ]
            }
        }
    }
]
```

## 输入数据
{word}

请严格按照上述格式输出补充后的单词信息，只返回JSON数据，不要包含任何解释或说明文字。
