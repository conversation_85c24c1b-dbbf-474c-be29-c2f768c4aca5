<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.CourseSectionStepMapper">

    <select id="statisticCourseReportData" resultType="org.nonamespace.word.server.dto.course.CourseReportDataDto">
        SELECT
            cs.type AS sectionType,
            t.name AS textbookName,
            COUNT(DISTINCT css.word_id) AS wordCnt
        FROM words.course_section_step css
        JOIN words.course_section cs ON css.section_id = cs.id
        JOIN words.textbook t ON css.textbook_id = t.id
        where
            cs."type" in ('新课程学习', '抗遗忘复习') AND css.deleted = FALSE AND cs.deleted = FALSE AND t.deleted = FALSE
            and css.course_id = #{courseId}
        GROUP BY cs.type, t.name
    </select>

</mapper>