import request from '@/utils/request'

// 获取学生列表（销售权限版本）
export function getStudentListApi(params) {
  return request({
    url: '/management/students',
    method: 'get',
    params
  })
}

// 获取学生详情
export function getStudentDetailApi(id) {
  return request({
    url: `/student-management/${id}`,
    method: 'get'
  })
}

// 获取学生课表
export function getStudentScheduleApi(id, params) {
  return request({
    url: `/student-management/${id}/schedule`,
    method: 'get',
    params
  })
}

// 获取学生课程统计
export function getStudentCourseStatsApi(id) {
  return request({
    url: `/student-management/${id}/course-stats`,
    method: 'get'
  })
}

// 获取学生最近课程
export function getStudentRecentCoursesApi(id, params) {
  return request({
    url: `/student-management/${id}/recent-courses`,
    method: 'get',
    params
  })
}

// 导出学生列表
export function exportStudentListApi(params) {
  return request({
    url: '/student-management/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取学生管理统计信息
export function getStudentManagementStatsApi() {
  return request({
    url: '/student-management/stats',
    method: 'get'
  })
}

// 更新学生信息（销售权限版本）
export function updateStudentInfoApi(id, data) {
  return request({
    url: `/student-management/${id}`,
    method: 'put',
    data
  })
}
