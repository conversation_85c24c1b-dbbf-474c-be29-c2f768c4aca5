【P6SPY-SQL】耗时: 30ms | statement | 连接ID: 0%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 1%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 2%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 29ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    config_id,config_name,config_key,config_value,config_type,create_by,create_time,update_by,update_time,remark    FROM  sys_config%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark  		from sys_dict_data       		 WHERE  status = ?  		order by dict_sort asc%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark 		from sys_job%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 3%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
