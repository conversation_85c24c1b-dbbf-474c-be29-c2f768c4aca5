【P6SPY-SQL】耗时: 30ms | statement | 连接ID: 0%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 1%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 2%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 29ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    config_id,config_name,config_key,config_value,config_type,create_by,create_time,update_by,update_time,remark    FROM  sys_config%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark  		from sys_dict_data       		 WHERE  status = ?  		order by dict_sort asc%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark 		from sys_job%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 3%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted FROM product WHERE id=? AND deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted FROM product WHERE id=? AND deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 27ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted FROM product WHERE id=? AND deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 19ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 19ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM teaching_group WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT    id,name,description,dept_id,leader_id,admin_id,status,remark,member_count,create_time,update_time,create_by,update_by,deleted    FROM  teaching_group     WHERE deleted=false     AND (deleted = ?) ORDER BY create_time DESC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 146ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,teacher_id,nick_name,phonenumber,real_name,gender,age,avatar,current_location,employment_type,current_status,education,graduate_school,major,university_type,is_normal_university,study_abroad,study_abroad_country,teaching_certificate_level,subjects,training_subjects,english_qualification,mandarin_qualification,communication_ability,teaching_experience,taught_courses,awards,teaching_years,teaching_style,english_pronunciation,suitable_grades,suitable_levels,suitable_personality,other,introduction,status,summer_schedule_type,formal_entry_date,qualification_certificates,demo_videos,create_time,update_time,create_by,update_by,deleted    FROM  teacher_profile     WHERE deleted=false     AND (deleted = ?) ORDER BY create_time DESC%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 42ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT    id,group_id,teacher_id,role_type,join_time,status,create_time,update_time,create_by,update_by,deleted    FROM  teaching_group_member     WHERE deleted=false     AND (group_id IN (?,?,?,?,?,?,?,?,?,?,?) AND deleted = ? AND status = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 3%n--- 完整SQL:%nSELECT    id,teacher_id,nick_name,phonenumber,real_name,gender,age,avatar,current_location,employment_type,current_status,education,graduate_school,major,university_type,is_normal_university,study_abroad,study_abroad_country,teaching_certificate_level,subjects,training_subjects,english_qualification,mandarin_qualification,communication_ability,teaching_experience,taught_courses,awards,teaching_years,teaching_style,english_pronunciation,suitable_grades,suitable_levels,suitable_personality,other,introduction,status,summer_schedule_type,formal_entry_date,qualification_certificates,demo_videos,create_time,update_time,create_by,update_by,deleted    FROM  teacher_profile     WHERE deleted=false     AND (teacher_id IN (?,?,?,?,?,?) AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 3%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | statement | 连接ID: 4%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 4%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 62ms | statement | 连接ID: 5%n--- 完整SQL:%ninsert into sys_logininfor (user_name, status, ipaddr, login_location, browser, os, msg, login_time) 		values (?, ?, ?, ?, ?, ?, ?, current_timestamp)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 5%n--- 完整SQL:%nselect config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark 		from sys_config                 		 WHERE  config_key = ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 5%n--- 完整SQL:%nselect u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,          d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,         r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status         from sys_user u 		    left join sys_dept d on u.dept_id = d.dept_id 		    left join sys_user_role ur on u.user_id = ur.user_id 		    left join sys_role r on r.role_id = ur.role_id       		where u.user_name = ? and u.del_flag = '0'%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 5%n--- 完整SQL:%nupdate sys_user  		 SET login_ip = ?,  			login_date = ?,  			  			  			update_time = current_timestamp   		where user_id = ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 5%n--- 完整SQL:%ninsert into sys_logininfor (user_name, status, ipaddr, login_location, browser, os, msg, login_time) 		values (?, ?, ?, ?, ?, ?, ?, current_timestamp)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 6%n--- 完整SQL:%nSELECT version()%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 6%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 25ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT     id,teacher_id,student_id,type,course_type,scheduled_start_time,scheduled_end_time,actual_start_time,actual_end_time,duration_minutes,course_status,external_course_id,cource_summary_note,stat_word_cnt,stat_word_learn_cnt,stat_word_mistakes_cnt,stat_word_review_cnt,current_section_index,word_pdf_url,practices_pdf_url,practices_pdf_url1,error_handout_pdf_url,error_exercise_pdf_url,cancel_type,cancel_reason,consumption_method,consumption_info,subject,specification,schedule_id,exception_types,create_time,update_time,create_by,update_by,deleted     FROM  course     WHERE deleted=false     AND (teacher_id = ? AND course_status IN (?,?,?)) ORDER BY scheduled_start_time ASC%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 31ms | statement | 连接ID: 6%n--- 完整SQL:%nSELECT             rs.id,             rs.student_id,             u.nick_name as student_name,             rs.course_id,             rs.review_type,             rs.name,             rs.scheduled_time,             rs.actual_start_time,             rs.actual_end_time,             rs.status,             rs.word_ids,             rs.textbook_item_ids,             rs.version,             rs.stat_word_total,             rs.stat_word_correct,             rs.stat_word_incorrect,             rs.stat_step_total,             rs.stat_step_correct,             rs.stat_step_incorrect,             rs.review_course_id,             rs.review_by_oneself,             rs.review_by_user_id,             rs.uploaded_images,             rs.uploaded_description,             rs.uploaded_time,             rs.create_time,             rs.update_time         FROM review_schedule rs         LEFT JOIN sys_user u ON rs.student_id = u.user_id::varchar                          WHERE rs.deleted = false                                           AND (                     EXISTS (                     SELECT 1 FROM teacher_student_relation tsr                     WHERE tsr.student_id = rs.student_id                     AND tsr.teacher_id = ?                     AND tsr.deleted = false                     AND tsr.status = 'active'                     )                     OR rs.review_by_user_id = ?                 )                                                                     AND rs.status IN                 (                     ?                 ,                     ?                 )                          ORDER BY                           rs.scheduled_time                                         ASC%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | commit | 连接ID: 6%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 34ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT             rs.id,             rs.student_id,             u.nick_name as student_name,             rs.course_id,             rs.review_type,             rs.name,             rs.scheduled_time,             rs.actual_start_time,             rs.actual_end_time,             rs.status,             rs.word_ids,             rs.textbook_item_ids,             rs.version,             rs.stat_word_total,             rs.stat_word_correct,             rs.stat_word_incorrect,             rs.stat_step_total,             rs.stat_step_correct,             rs.stat_step_incorrect,             rs.review_course_id,             rs.review_by_oneself,             rs.review_by_user_id,             rs.uploaded_images,             rs.uploaded_description,             rs.uploaded_time,             rs.create_time,             rs.update_time         FROM review_schedule rs         LEFT JOIN sys_user u ON rs.student_id = u.user_id::varchar                          WHERE rs.deleted = false                                           AND (                     EXISTS (                     SELECT 1 FROM teacher_student_relation tsr                     WHERE tsr.student_id = rs.student_id                     AND tsr.teacher_id = ?                     AND tsr.deleted = false                     AND tsr.status = 'active'                     )                     OR rs.review_by_user_id = ?                 )                                                                     AND rs.status IN                 (                     ?                 ,                     ?                 )                          ORDER BY                           rs.scheduled_time                                         ASC%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 25ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 28ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 23ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 5%n--- 完整SQL:%nselect distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query, m.visible, m.status, nullif(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time 		from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 		order by m.parent_id, m.order_num%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 25ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT COUNT( * ) AS total FROM  teacher_profile     WHERE deleted=false     AND (teacher_id = ? AND deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 25ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 22ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 20ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 24ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 26ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT COUNT(*) AS total FROM product WHERE deleted = false AND (deleted = ?)%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 29ms | statement | 连接ID: 5%n--- 完整SQL:%nSELECT    id,name,type,description,subject,course_type,applicable_grades,unit_price,quantity,has_bonus_hours,bonus_hours_quantity,has_material_fee,material_fee,original_price,selling_price,status,cover_image,detail_images,sort_order,remark,stock,stock_limited,sales_count,tags,content,create_time,update_time,create_by,update_by,deleted    FROM  product     WHERE deleted=false     AND (deleted = ?) ORDER BY sort_order ASC LIMIT ?%n--- 调用来源:%n%(stackTrace)
【P6SPY-SQL】耗时: 21ms | commit | 连接ID: 5%n--- 完整SQL:%n%n--- 调用来源:%n%(stackTrace)
