<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>产品下单</span>
      </div>
      
      <!-- 步骤条 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择产品"></el-step>
        <el-step title="选择学生"></el-step>
        <el-step title="确认订单"></el-step>
        <el-step title="支付"></el-step>
      </el-steps>

      <!-- 步骤1: 选择产品 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>选择产品</h3>
        <div v-if="presetProductId && selectedProduct" class="preset-info">
          <el-alert
            title="已预选产品"
            :description="`产品：${selectedProduct.name} - ¥${(selectedProduct.price / 100).toFixed(2)}`"
            type="info"
            show-icon
            :closable="false"
          />
          <div style="margin-top: 15px;">
            <el-button type="text" @click="changeProduct">更换产品</el-button>
          </div>
        </div>
        <el-row v-else :gutter="20">
          <el-col :span="6" v-for="product in products" :key="product.id">
            <el-card
              :class="['product-card', { 'selected': selectedProduct && selectedProduct.id === product.id }]"
              @click.native="selectProduct(product)"
            >
              <img v-if="product.coverImage" :src="product.coverImage" class="product-image">
              <div class="product-info">
                <h4>{{ product.name }}</h4>
                <p class="product-desc">{{ product.description }}</p>
                <div class="product-meta">
                  <span class="product-type">{{ product.type }}</span>
                  <span class="product-subject">{{ product.subject }}</span>
                </div>
                <div class="product-price">
                  <span class="price">¥{{ (product.price / 100).toFixed(2) }}</span>
                  <span v-if="product.hours" class="hours">{{ product.hours }}课时</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <div class="step-actions">
          <el-button type="primary" :disabled="!selectedProduct" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2: 选择学生 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>选择学生</h3>
        <div v-if="presetStudentId && selectedStudent" class="preset-info">
          <el-alert
            title="已预选学生"
            :description="`学生：${selectedStudent.name} (${selectedStudent.phone || ''})`"
            type="info"
            show-icon
            :closable="false"
          />
          <div style="margin-top: 15px;">
            <el-button type="text" @click="changeStudent">更换学生</el-button>
          </div>
        </div>
        <el-form v-else :model="orderForm" label-width="100px">
          <el-form-item label="学生">
            <el-select
              v-model="orderForm.studentId"
              placeholder="请选择学生"
              filterable
              remote
              :remote-method="searchStudents"
              :loading="studentLoading"
              style="width: 100%"
            >
              <el-option
                v-for="student in students"
                :key="student.id"
                :label="`${student.name} (${student.phone})`"
                :value="student.id"
              >
                <span style="float: left">{{ student.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ student.phone }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :disabled="!orderForm.studentId" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤3: 确认订单 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>确认订单信息</h3>
        <el-form :model="orderForm" label-width="120px">
          <el-form-item label="产品信息">
            <div class="order-product-info">
              <h4>{{ selectedProduct.name }}</h4>
              <p>{{ selectedProduct.description }}</p>
              <p>价格: ¥{{ (selectedProduct.price / 100).toFixed(2) }}</p>
              <p v-if="selectedProduct.hours">课时: {{ selectedProduct.hours }}课时</p>
            </div>
          </el-form-item>
          <el-form-item label="学生信息">
            <div class="order-student-info">
              <p>{{ selectedStudent.name }}</p>
              <p>{{ selectedStudent.phone }}</p>
            </div>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-radio-group v-model="orderForm.paymentType">
              <el-radio label="once">一次性付款</el-radio>
              <el-radio label="installment">分期付款</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="orderForm.paymentType === 'installment'" label="分期设置">
            <div class="installment-settings">
              <el-button @click="addInstallment" type="primary" size="small">添加分期</el-button>
              <div v-for="(installment, index) in orderForm.installments" :key="index" class="installment-item">
                <el-input-number 
                  v-model="installment.amount" 
                  :min="1" 
                  :max="selectedProduct.price"
                  placeholder="金额(分)"
                  size="small"
                ></el-input-number>
                <span>第{{ index + 1 }}期</span>
                <el-button @click="removeInstallment(index)" type="danger" size="small">删除</el-button>
              </div>
              <p class="installment-total">
                总计: ¥{{ (installmentTotal / 100).toFixed(2) }} / ¥{{ (selectedProduct.price / 100).toFixed(2) }}
                <span :class="{ 'error': installmentTotal !== selectedProduct.price }">
                  {{ installmentTotal === selectedProduct.price ? '✓' : '✗' }}
                </span>
              </p>
            </div>
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :disabled="!isOrderValid" @click="nextStep">确认订单</el-button>
        </div>
      </div>

      <!-- 步骤4: 支付 -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>支付</h3>
        <div v-if="orderCreated" class="payment-info">
          <p>订单创建成功！订单号: {{ createdOrderId }}</p>
          <div class="payment-methods">
            <el-button type="primary" @click="generateQRCode">生成支付二维码</el-button>
            <el-button @click="copyPaymentLink">复制支付链接</el-button>
            <el-button @click="sendWechatMessage">发送微信模板消息</el-button>
          </div>
          <div v-if="qrCodeVisible && qrCodeData" class="qr-code-container">
            <h4>扫码支付</h4>
            <img :src="`data:image/png;base64,${qrCodeData.qrCodeBase64}`" alt="支付二维码" class="qr-code-image">
            <p class="qr-code-tip">请使用微信或支付宝扫描二维码完成支付</p>
          </div>
        </div>
        <div class="step-actions">
          <el-button @click="resetOrder">重新下单</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getAvailableProductsApi } from '@/api/management/product'
import { getStudentListApi } from '@/api/management/studentManagement'
import {
  createOrderApi,
  generateQRCodeApi,
  getPaymentLinkApi,
  getOrderTransactionsApi
} from '@/api/management/order'

export default {
  name: 'OrderManagement',
  data() {
    return {
      currentStep: 0,
      products: [],
      students: [],
      selectedProduct: null,
      selectedStudent: null,
      studentLoading: false,
      orderForm: {
        studentId: '',
        productId: '',
        paymentType: 'once',
        installments: []
      },
      orderCreated: false,
      createdOrderId: '',
      qrCodeVisible: false,
      qrCodeData: null,
      // 预设参数（从其他页面传入）
      presetStudentId: null,
      presetProductId: null
    }
  },
  computed: {
    installmentTotal() {
      return this.orderForm.installments.reduce((total, item) => total + (item.amount || 0), 0)
    },
    isOrderValid() {
      if (this.orderForm.paymentType === 'once') {
        return true
      } else {
        return this.installmentTotal === this.selectedProduct.price && this.orderForm.installments.length > 0
      }
    }
  },
  mounted() {
    this.initializeFromRoute()
    this.loadProducts()

    // 如果有预设学生ID，加载学生信息
    if (this.presetStudentId) {
      this.loadPresetStudent()
    }
  },
  methods: {
    /** 初始化路由参数 */
    initializeFromRoute() {
      // 从路由参数获取预设的学生ID和产品ID
      const { studentId, productId } = this.$route.query

      if (studentId) {
        this.presetStudentId = studentId
        this.orderForm.studentId = studentId
        console.log('预设学生ID:', studentId)
      }

      if (productId) {
        this.presetProductId = productId
        this.orderForm.productId = productId
        console.log('预设产品ID:', productId)
      }

      // 根据预设参数调整初始步骤
      this.determineInitialStep()
    },

    /** 确定初始步骤 */
    determineInitialStep() {
      if (this.presetProductId && this.presetStudentId) {
        // 如果产品和学生都预设了，直接跳到确认订单步骤
        this.currentStep = 2
      } else if (this.presetProductId) {
        // 如果只预设了产品，跳到选择学生步骤
        this.currentStep = 1
      } else if (this.presetStudentId) {
        // 如果只预设了学生，从选择产品开始
        this.currentStep = 0
      } else {
        // 都没有预设，从第一步开始
        this.currentStep = 0
      }
    },

    async loadProducts() {
      try {
        const response = await getAvailableProductsApi()
        this.products = response.data

        // 如果有预设的产品ID，自动选择该产品
        if (this.presetProductId) {
          this.selectedProduct = this.products.find(p => p.id === this.presetProductId)
          if (this.selectedProduct) {
            console.log('自动选择产品:', this.selectedProduct.name)
          } else {
            this.$message.warning('指定的产品不存在或已下架')
            // 重置到第一步
            this.currentStep = 0
            this.presetProductId = null
            this.orderForm.productId = ''
          }
        }
      } catch (error) {
        this.$message.error('加载产品列表失败')
      }
    },
    async searchStudents(query) {
      if (query !== '') {
        this.studentLoading = true
        try {
          const response = await getStudentListApi({ name: query, pageSize: 20 })
          this.students = response.data.records

          // 如果有预设的学生ID，自动选择该学生
          if (this.presetStudentId && this.students.length > 0) {
            this.selectedStudent = this.students.find(s => s.id === this.presetStudentId)
            if (this.selectedStudent) {
              console.log('自动选择学生:', this.selectedStudent.name)
            }
          }
        } catch (error) {
          this.$message.error('搜索学生失败')
        } finally {
          this.studentLoading = false
        }
      } else if (this.presetStudentId) {
        // 如果有预设学生ID但没有搜索词，直接加载该学生
        this.loadPresetStudent()
      }
    },

    /** 加载预设学生信息 */
    async loadPresetStudent() {
      if (!this.presetStudentId) return

      this.studentLoading = true
      try {
        // 这里应该调用根据ID获取学生详情的API
        // 暂时使用搜索API模拟
        const response = await getStudentListApi({ pageSize: 100 })
        const allStudents = response.data.records
        this.selectedStudent = allStudents.find(s => s.id === this.presetStudentId)

        if (this.selectedStudent) {
          this.students = [this.selectedStudent] // 将预设学生添加到选项中
          console.log('加载预设学生:', this.selectedStudent.name)
        } else {
          this.$message.warning('指定的学生不存在')
          this.presetStudentId = null
          this.orderForm.studentId = ''
        }
      } catch (error) {
        this.$message.error('加载学生信息失败')
      } finally {
        this.studentLoading = false
      }
    },
    selectProduct(product) {
      this.selectedProduct = product
      this.orderForm.productId = product.id
    },

    /** 更换产品 */
    changeProduct() {
      this.presetProductId = null
      this.selectedProduct = null
      this.orderForm.productId = ''
    },

    /** 更换学生 */
    changeStudent() {
      this.presetStudentId = null
      this.selectedStudent = null
      this.orderForm.studentId = ''
      this.students = []
    },
    nextStep() {
      if (this.currentStep === 1) {
        // 获取选中的学生信息（如果还没有选中的话）
        if (!this.selectedStudent) {
          this.selectedStudent = this.students.find(s => s.id === this.orderForm.studentId)
        }
      } else if (this.currentStep === 2) {
        // 创建订单
        this.createOrder()
        return
      }
      this.currentStep++
    },
    prevStep() {
      this.currentStep--
    },
    async createOrder() {
      try {
        const orderData = {
          studentId: this.orderForm.studentId,
          productId: this.orderForm.productId
        }
        
        if (this.orderForm.paymentType === 'installment') {
          orderData.multiTrxAmts = this.orderForm.installments.map((item, index) => ({
            idx: index + 1,
            amt: item.amount
          }))
        }
        
        const response = await createOrderApi(orderData)
        this.createdOrderId = response.data
        this.orderCreated = true
        this.currentStep++
        this.$message.success('订单创建成功')
      } catch (error) {
        this.$message.error('创建订单失败: ' + error.message)
      }
    },
    addInstallment() {
      this.orderForm.installments.push({ amount: 0 })
    },
    removeInstallment(index) {
      this.orderForm.installments.splice(index, 1)
    },
    async generateQRCode() {
      try {
        // 先获取订单的交易流水
        const transactionsResponse = await getOrderTransactionsApi(this.createdOrderId)
        const transactions = transactionsResponse.data
        if (transactions && transactions.length > 0) {
          const firstTransaction = transactions[0]
          const qrCodeResponse = await generateQRCodeApi(firstTransaction.id)
          this.qrCodeData = qrCodeResponse.data
          this.qrCodeVisible = true
          this.$message.success('支付二维码生成成功')
        } else {
          this.$message.error('未找到交易流水')
        }
      } catch (error) {
        this.$message.error('生成支付二维码失败: ' + error.message)
      }
    },
    async copyPaymentLink() {
      try {
        // 先获取订单的交易流水
        const transactionsResponse = await getOrderTransactionsApi(this.createdOrderId)
        const transactions = transactionsResponse.data
        if (transactions && transactions.length > 0) {
          const firstTransaction = transactions[0]
          const linkResponse = await getPaymentLinkApi(firstTransaction.id)
          const paymentLink = linkResponse.data

          // 复制到剪贴板
          if (navigator.clipboard) {
            await navigator.clipboard.writeText(paymentLink)
            this.$message.success('支付链接已复制到剪贴板')
          } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea')
            textArea.value = paymentLink
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            this.$message.success('支付链接已复制到剪贴板')
          }
        } else {
          this.$message.error('未找到交易流水')
        }
      } catch (error) {
        this.$message.error('复制支付链接失败: ' + error.message)
      }
    },
    sendWechatMessage() {
      // 发送微信模板消息的逻辑
      this.$message.success('微信模板消息发送成功')
    },
    resetOrder() {
      this.currentStep = 0
      this.selectedProduct = null
      this.selectedStudent = null
      this.orderForm = {
        studentId: '',
        productId: '',
        paymentType: 'once',
        installments: []
      }
      this.orderCreated = false
      this.createdOrderId = ''
      this.qrCodeVisible = false
      this.qrCodeData = null
    }
  }
}
</script>

<style scoped>
.step-content {
  margin: 30px 0;
  min-height: 400px;
}

.product-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-card.selected {
  border-color: #409EFF;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.product-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.product-info {
  padding: 10px;
}

.product-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.product-desc {
  color: #666;
  font-size: 12px;
  margin: 0 0 8px 0;
}

.product-meta {
  margin-bottom: 8px;
}

.product-type, .product-subject {
  display: inline-block;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  margin-right: 5px;
}

.product-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 16px;
}

.hours {
  color: #666;
  font-size: 12px;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
}

.order-product-info, .order-student-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
}

.installment-settings {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
}

.installment-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.installment-total {
  margin-top: 10px;
  font-weight: bold;
}

.installment-total .error {
  color: #e74c3c;
}

.payment-info {
  text-align: center;
}

.payment-methods {
  margin: 20px 0;
}

.qr-code-container {
  margin-top: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.qr-code-tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.preset-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}
</style>
