package org.nonamespace.word.rest.controller.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderDto;
import org.nonamespace.word.server.dto.order.PaymentDto;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Validated
public class OrderController extends BaseController {

    private final IOrdersService ordersService;
    private final IOrdersTrxService ordersTrxService;

    /**
     * 创建订单
     */
    @PreAuthorize("@ss.hasPermi('order:create')")
    @Log(title = "订单管理", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createOrder(@Valid @RequestBody OrderCreateDto orderCreateDto) {
        try {
            String orderId = ordersService.createOrder(orderCreateDto);
            return AjaxResult.success("订单创建成功", orderId);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询订单详情
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable String orderId) {
        try {
            Orders order = ordersService.getById(orderId);
            if (order == null) {
                return error("订单不存在");
            }
            return AjaxResult.success(order);
        } catch (Exception e) {
            log.error("查询订单详情失败", e);
            return error("查询订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号查询订单详情
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/no/{orderNo}")
    public AjaxResult getOrderByNo(@PathVariable String orderNo) {
        try {
            Orders order = ordersService.getByOrderNo(orderNo);
            if (order == null) {
                return error("订单不存在");
            }
            return AjaxResult.success(order);
        } catch (Exception e) {
            log.error("根据订单号查询订单失败", e);
            return error("根据订单号查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据学生ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/student/{studentId}")
    public AjaxResult getOrdersByStudent(@PathVariable String studentId) {
        try {
            List<Orders> orders = ordersService.getByStudentId(studentId);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据学生ID查询订单列表失败", e);
            return error("根据学生ID查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据销售员ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/saler/{salerId}")
    public AjaxResult getOrdersBySaler(@PathVariable String salerId) {
        try {
            List<Orders> orders = ordersService.getBySalerId(salerId);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据销售员ID查询订单列表失败", e);
            return error("根据销售员ID查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单状态查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/status/{orderStatus}")
    public AjaxResult getOrdersByStatus(@PathVariable String orderStatus) {
        try {
            List<Orders> orders = ordersService.getByOrderStatus(orderStatus);
            return AjaxResult.success(orders);
        } catch (Exception e) {
            log.error("根据订单状态查询订单列表失败", e);
            return error("根据订单状态查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询交易流水列表
     */
    @PreAuthorize("@ss.hasPermi('order:query')")
    @GetMapping("/{orderId}/transactions")
    public AjaxResult getOrderTransactions(@PathVariable String orderId) {
        try {
            List<OrdersTrx> transactions = ordersTrxService.getByOrderId(orderId);
            return AjaxResult.success(transactions);
        } catch (Exception e) {
            log.error("查询订单交易流水失败", e);
            return error("查询订单交易流水失败: " + e.getMessage());
        }
    }

    /**
     * 发起支付（生成支付二维码）
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @Log(title = "订单支付", businessType = BusinessType.UPDATE)
    @PostMapping("/pay/{orderTrxId}")
    public AjaxResult nativePay(@PathVariable String orderTrxId) {
        try {
            ordersService.nativePay(orderTrxId);
            return AjaxResult.success("支付信息生成成功");
        } catch (Exception e) {
            log.error("生成支付信息失败", e);
            return error("生成支付信息失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PreAuthorize("@ss.hasPermi('order:cancel')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@PathVariable String orderId) {
        try {
            ordersService.cancelOrder(orderId);
            return AjaxResult.success("订单取消成功");
        } catch (Exception e) {
            log.error("取消订单失败", e);
            return error("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 生成支付信息
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @PostMapping("/payment/{orderTrxId}")
    public AjaxResult generatePayment(@PathVariable String orderTrxId) {
        try {
            PaymentDto.PayResp payResp = ordersService.generatePayment(orderTrxId);
            return AjaxResult.success(payResp);
        } catch (Exception e) {
            log.error("生成支付信息失败", e);
            return error("生成支付信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成支付二维码
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @PostMapping("/qrcode/{orderTrxId}")
    public AjaxResult generateQRCode(@PathVariable String orderTrxId) {
        try {
            PaymentDto.QRCodeResp qrCodeResp = ordersService.generateQRCode(orderTrxId);
            return AjaxResult.success(qrCodeResp);
        } catch (Exception e) {
            log.error("生成支付二维码失败", e);
            return error("生成支付二维码失败: " + e.getMessage());
        }
    }

    /**
     * 复制支付链接
     */
    @PreAuthorize("@ss.hasPermi('order:pay')")
    @GetMapping("/payment-link/{orderTrxId}")
    public AjaxResult getPaymentLink(@PathVariable String orderTrxId) {
        try {
            PaymentDto.PayResp payResp = ordersService.generatePayment(orderTrxId);
            return AjaxResult.success("支付链接获取成功", payResp.getPayUrl());
        } catch (Exception e) {
            log.error("获取支付链接失败", e);
            return error("获取支付链接失败: " + e.getMessage());
        }
    }
}
