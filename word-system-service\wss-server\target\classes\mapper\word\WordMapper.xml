<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.WordMapper">

    <select id="selectPageByParam" resultType="org.nonamespace.word.server.dto.WordPageDto$Resp" parameterType="org.nonamespace.word.server.dto.WordPageDto$Req">
        with result_words as (
            select * from word where deleted = false
        )
        select
        rw.id as wordId,
        rw.word,
        rw.syllables,
        rw.phonetic_uk as phoneticUk,
        rw.phonetic_us as phoneticUs,
        rw.audio_uk_url as audioUkUrl,
        rw.audio_us_url as audioUsUrl,
        rw.meanings as meaningsStr,
        rw.sentences as sentencesStr,
        rw.tags,
        rw.difficulty,
        rw.video_url as videoUrlStr
        from result_words rw
        <if test="qryTextbook != null and qryTextbook == true">
            left join textbook_item tbi ON rw.id = tbi.word_id and tbi.deleted = false
            LEFT JOIN textbook tb ON tbi.textbook_id = tb.ID
            <where>
                <if test="textbookId != null and textbookId != ''">
                    and tbi.textbook_id = #{textbookId}
                </if>
                <if test="unitId != null and unitId != ''">
                    and tbi.pid = #{unitId} and tbi.node_type = 3
                </if>
                <if test="textBookType != null and textBookType != ''">
                    and tb.type = #{textBookType}
                </if>
                <if test="textBookName != null and textBookName.value != null and textBookName.value != '' and textBookName.compare.name() == 'EQ'">
                    and tb.name = #{textBookName.value}
                </if>
                <if test="textBookName != null and textBookName.value != null and textBookName.value != '' and textBookName.compare.name() == 'LIKE'">
                    and tb.name like concat('%', #{textBookName.value}::text, '%')
                </if>
                <if test="word != null and word.value != null and word.value != '' and word.compare.name() == 'EQ'">
                    and rw.word = #{word.value}
                </if>
                <if test="word != null and word.value != null and word.value != '' and word.compare.name() == 'LIKE'">
                    and rw.word like concat('%', #{word.value}::text, '%')
                </if>
            </where>
        </if>
        <if test="qryTextbook == null or qryTextbook == false">
            <where>
                <if test="word != null and word.value != null and word.value != '' and word.compare.name() == 'EQ'">
                    and rw.word = #{word.value}
                </if>
                <if test="word != null and word.value != null and word.value != '' and word.compare.name() == 'LIKE'">
                    and rw.word like concat('%', #{word.value}::text, '%')
                </if>
            </where>
        </if>
        <if test="qryTextbook != null and qryTextbook == true">
            order by
            CASE WHEN tbi.display_order IS NOT NULL THEN 0
            ELSE 1 END, tbi.display_order ASC
        </if>
        <if test="qryTextbook == null or qryTextbook == false">
            order by rw.word
        </if>
    </select>


    <select id="listUnRichWords" resultMap="wordResultMap">
        SELECT * FROM word
        WHERE (
                  -- 检查meanings字段问题
                  (
                      meanings IS NULL
                          OR jsonb_typeof(meanings) != 'object'
                            OR EXISTS (
                                SELECT 1 FROM jsonb_each(meanings) AS category(category_name, category_data)
                                WHERE (
                                    category_data -> 'pos' IS NULL
                                    OR jsonb_typeof(category_data -> 'pos') != 'array'
                                    OR category_data -> 'pos' = '[]'::jsonb
                                    OR EXISTS (
                                        SELECT 1
                                        FROM jsonb_array_elements(CASE
                                            WHEN jsonb_typeof(category_data -> 'pos') = 'array'
                                            THEN category_data -> 'pos'
                                            ELSE '[]'::jsonb
                                        END) AS pos_element
                                        WHERE (pos_element ->> 'def' = '' OR pos_element ->> 'def' IS NULL)
                                           OR (pos_element ->> 'pos' = '' OR pos_element ->> 'pos' IS NULL)
                                    )
                                )
                                OR (
                                    category_data -> 'practices' IS NULL
                                    OR jsonb_typeof(category_data -> 'practices') != 'array'
                                    OR category_data -> 'practices' = '[]'::jsonb
                                )
                            )
                      )
                      OR
                      -- 检查sentences字段问题
                  (
                      sentences IS NULL
                          OR jsonb_typeof(sentences) != 'object'
                            OR EXISTS (
                                SELECT 1
                                FROM jsonb_each(sentences) AS category(category_name, sentences_array),
                                jsonb_array_elements(
                                    CASE
                                        WHEN jsonb_typeof(sentences_array) = 'array'
                                        THEN sentences_array
                                        ELSE '[]'::jsonb
                                    END
                                ) AS example_element
                                WHERE (
                                    example_element -> 'practices' IS NULL
                                    OR jsonb_typeof(example_element -> 'practices') != 'array'
                                    OR example_element -> 'practices' = '[]'::jsonb
                                    OR (example_element ->> 'sentenceCn' = '' OR example_element ->> 'sentenceCn' IS NULL)
                                    OR (example_element ->> 'sentenceEn' = '' OR example_element ->> 'sentenceEn' IS NULL)
                                    OR example_element -> 'structurePartsEn' IS NULL
                                    OR jsonb_typeof(example_element -> 'structurePartsEn') != 'array'
                                    OR example_element -> 'structurePartsEn' = '[]'::jsonb
                                )
                            )
                          OR EXISTS (
                                SELECT 1
                                FROM jsonb_array_elements(
                                    CASE
                                        WHEN jsonb_typeof(sentences -> '通用') = 'array'
                                        THEN sentences -> '通用'
                                        ELSE '[]'::jsonb
                                    END
                                ) AS common_sentence
                                WHERE (common_sentence -> 'stage') IS NULL
                                   OR jsonb_typeof(common_sentence -> 'stage') = 'null'
                                   OR (common_sentence ->> 'stage') = ''
                            )
                      )
                      OR phonetic_uk IS NULL
                      OR phonetic_uk = ''
                      OR phonetic_us IS NULL
                      OR phonetic_us = ''
                      OR syllables IS NULL
                      OR syllables = ''
                  )
    </select>

    <select id="listUnRichBasic" resultMap="wordResultMap">
        SELECT * FROM word
        WHERE (
          phonetic_uk IS NULL
              OR phonetic_uk = ''
              OR phonetic_us IS NULL
              OR phonetic_us = ''
              OR syllables IS NULL
              OR syllables = ''
          )
    </select>

    <select id="listUnRichMeanings" resultMap="wordResultMap">
            SELECT * FROM word
            where meanings::text like '%[]%'
            or meanings::text like '%""%'
            or meanings is null
            or meanings::text like '%{}%'
            OR EXISTS (
                SELECT 1
                FROM jsonb_each( CASE WHEN jsonb_typeof(word.meanings) = 'object' THEN word.meanings ELSE '{}'::jsonb END ) AS top_level_entry
                WHERE jsonb_typeof(top_level_entry.value) = 'object'
                AND jsonb_typeof(top_level_entry.value -> 'practices') = 'array'
                AND jsonb_array_length(top_level_entry.value -> 'practices') &lt; 3
            );
    </select>


    <select id="listUnRichSentences" resultMap="wordResultMap">
            SELECT * FROM word
            where sentences::text like '%[]%'
               or sentences::text like '%""%'
               or sentences is null
            or sentences::text like '%{}%'
            OR EXISTS (
                SELECT 1 FROM
                    jsonb_each(CASE WHEN jsonb_typeof(word.sentences) = 'object' THEN word.sentences ELSE '{}'::jsonb END ) AS top_level_entry,
                    jsonb_array_elements( CASE WHEN jsonb_typeof(top_level_entry.value) = 'array' THEN top_level_entry.value ELSE '[]'::jsonb END ) AS entry
                WHERE
                jsonb_typeof(entry) = 'object'
                AND jsonb_typeof(entry -> 'practices') = 'array'
                AND jsonb_array_length(entry -> 'practices') &lt; 3
            );
    </select>

    <resultMap id="wordResultMap" type="org.nonamespace.word.server.domain.Word">
        <!-- 基础字段 -->
        <id column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
        <result column="change_description" property="changeDescription"/>

        <!-- 单词基本信息 -->
        <result column="word" property="word"/>
        <result column="syllables" property="syllables"/>
        <result column="phonetic_uk" property="phoneticUk"/>
        <result column="phonetic_us" property="phoneticUs"/>
        <result column="audio_uk_url" property="audioUkUrl"/>
        <result column="audio_us_url" property="audioUsUrl"/>

        <!-- 复杂类型字段（使用类型处理器） -->
        <result column="meanings" property="meanings"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="sentences" property="sentences"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="tags" property="tags"
                typeHandler="org.nonamespace.word.server.misc.mybatis.ListStringTypeHandler"/>
        <result column="video_url" property="videoUrl"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <!-- 难度等级 -->
        <result column="difficulty" property="difficulty"/>

        <!-- 练习标记 -->
        <result column="flag_practice_word" property="flagPracticeWord"/>
        <result column="flag_practice_translate" property="flagPracticeTranslate"/>
        <result column="flag_practice_order" property="flagPracticeOrder"/>
    </resultMap>

    <select id="listUnRichSentencesAudio" resultMap="wordResultMap">
        SELECT
            *
        FROM
            word
        WHERE
            jsonb_typeof(sentences) = 'object' -- 确保 sentences 是一个JSON对象
          AND (
            EXISTS (
                SELECT
                    1
                FROM
                    jsonb_array_elements(
                            CASE
                                WHEN jsonb_typeof(sentences -> '通用') = 'array' THEN sentences -> '通用'
                                ELSE '[]'::jsonb -- 如果不是数组，则提供一个空数组，避免错误
                                END
                    ) AS elem
                WHERE
                    (elem ->> 'audioUkUrl' IS NULL OR elem ->> 'audioUkUrl' = '')
                   OR (elem ->> 'audioUsUrl' IS NULL OR elem ->> 'audioUsUrl' = '')
            )
                OR EXISTS (
                SELECT
                    1
                FROM
                    jsonb_array_elements(
                            CASE
                                WHEN jsonb_typeof(sentences -> '特色') = 'array' THEN sentences -> '特色'
                                ELSE '[]'::jsonb -- 如果不是数组，则提供一个空数组，避免错误
                                END
                    ) AS elem
                WHERE
                    (elem ->> 'audioUkUrl' IS NULL OR elem ->> 'audioUkUrl' = '')
                   OR (elem ->> 'audioUsUrl' IS NULL OR elem ->> 'audioUsUrl' = '')
            )
            )
    </select>

    <select id="selectRandomList" parameterType="int" resultMap="wordResultMap">
        SELECT * FROM word WHERE random() &lt; 0.01 and meanings is not null
        and meanings::text not like '%[]%'
        and meanings::text not like '%""%'
        and meanings is not null
        and sentences::text not like '%[]%'
        and sentences::text not like '%""%'
        and sentences is not null
        ORDER BY random() LIMIT #{count}

    </select>


    <!-- 查找出sentences Practices 内容包含英文字符的单词，需要补全-->
    <select id="selectSentencesPracticesContainsEn" resultMap="wordResultMap">
        SELECT * FROM word
        WHERE EXISTS (
            SELECT 1
            FROM jsonb_each(sentences) AS entry
            WHERE
                jsonb_typeof(entry.value) = 'array'
              AND EXISTS (
                SELECT 1
                FROM jsonb_array_elements(entry.value) AS item
                WHERE
                    jsonb_typeof(item->'practices') = 'array'
                  AND EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(item->'practices') AS practice_text
                    WHERE
                        practice_text ~ '[a-zA-Z]'
                )
            )
        );
    </select>

</mapper>