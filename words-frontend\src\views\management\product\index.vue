<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课型" prop="courseType">
        <el-select v-model="queryParams.courseType" placeholder="请选择课型" clearable>
          <el-option label="基础课" value="基础课" />
          <el-option label="提升课" value="提升课" />
          <el-option label="专项课" value="专项课" />
          <el-option label="冲刺课" value="冲刺课" />
          <el-option label="思维课" value="思维课" />
          <el-option label="实验课" value="实验课" />
        </el-select>
      </el-form-item>
      <el-form-item label="学科" prop="subject">
        <el-select v-model="queryParams.subject" placeholder="请选择学科" clearable>
          <el-option label="英语" value="英语" />
          <el-option label="数学" value="数学" />
          <el-option label="语文" value="语文" />
          <el-option label="物理" value="物理" />
          <el-option label="化学" value="化学" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="上架" value="上架" />
          <el-option label="下架" value="下架" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 产品列表 -->
    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="产品名称" align="center" prop="name" />
      <el-table-column label="学科" align="center" prop="subject" />
      <el-table-column label="课型" align="center" prop="courseType" />
      <el-table-column label="适用年级" align="center" prop="applicableGrades">
        <template #default="{ row }">
          <span v-if="row.applicableGrades && row.applicableGrades.length">
            {{ row.applicableGrades.join(', ') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="单价" align="center" prop="unitPrice">
        <template #default="{ row }">
          ¥{{ (row.unitPrice / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="quantity" />
      <el-table-column label="原价" align="center" prop="originalPrice">
        <template #default="{ row }">
          ¥{{ (row.originalPrice / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="售价" align="center" prop="sellingPrice">
        <template #default="{ row }">
          ¥{{ (row.sellingPrice / 100).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="{ row }">
          <el-tag :type="row.status === '上架' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          >修改</el-button>
          <el-button
            v-if="row.status === '下架'"
            size="mini"
            type="text"
            icon="el-icon-top"
            @click="handleEnable(row)"
          >上架</el-button>
          <el-button
            v-if="row.status === '上架'"
            size="mini"
            type="text"
            icon="el-icon-bottom"
            @click="handleDisable(row)"
          >下架</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="产品描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入产品描述" />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="form.subject" placeholder="请选择学科">
            <el-option label="英语" value="英语" />
            <el-option label="数学" value="数学" />
            <el-option label="语文" value="语文" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="courseType">
          <el-select v-model="form.courseType" placeholder="请选择课型">
            <el-option label="基础课" value="基础课" />
            <el-option label="提升课" value="提升课" />
            <el-option label="专项课" value="专项课" />
            <el-option label="冲刺课" value="冲刺课" />
            <el-option label="思维课" value="思维课" />
            <el-option label="实验课" value="实验课" />
          </el-select>
        </el-form-item>
        <el-form-item label="适用年级" prop="applicableGrades">
          <el-select v-model="form.applicableGrades" multiple placeholder="请选择适用年级">
            <el-option label="小学1年级" value="小学1年级" />
            <el-option label="小学2年级" value="小学2年级" />
            <el-option label="小学3年级" value="小学3年级" />
            <el-option label="小学4年级" value="小学4年级" />
            <el-option label="小学5年级" value="小学5年级" />
            <el-option label="小学6年级" value="小学6年级" />
            <el-option label="初中1年级" value="初中1年级" />
            <el-option label="初中2年级" value="初中2年级" />
            <el-option label="初中3年级" value="初中3年级" />
            <el-option label="高中1年级" value="高中1年级" />
            <el-option label="高中2年级" value="高中2年级" />
            <el-option label="高中3年级" value="高中3年级" />
          </el-select>
        </el-form-item>
        <el-form-item label="单价(元)" prop="unitPrice">
          <el-input-number v-model="unitPriceInYuan" :precision="2" :min="0" placeholder="请输入单价" @change="calculateOriginalPrice" />
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number v-model="form.quantity" :min="1" placeholder="请输入数量" @change="calculateOriginalPrice" />
        </el-form-item>
        <el-form-item label="赠送课时">
          <el-checkbox v-model="form.hasBonusHours" @change="onBonusHoursChange">是否有赠送课时</el-checkbox>
        </el-form-item>
        <el-form-item v-if="form.hasBonusHours" label="赠送数量" prop="bonusHoursQuantity">
          <el-input-number v-model="form.bonusHoursQuantity" :min="0" placeholder="请输入赠送课时数量" />
        </el-form-item>
        <el-form-item label="教材费">
          <el-checkbox v-model="form.hasMaterialFee" @change="onMaterialFeeChange">是否包含教材费</el-checkbox>
        </el-form-item>
        <el-form-item v-if="form.hasMaterialFee" label="教材费(元)" prop="materialFee">
          <el-input-number v-model="materialFeeInYuan" :precision="2" :min="0" placeholder="请输入教材费" @change="calculateOriginalPrice" />
        </el-form-item>
        <el-form-item label="原价(元)">
          <el-input-number v-model="originalPriceInYuan" :precision="2" disabled />
          <span style="color: #999; font-size: 12px; margin-left: 10px;">自动计算：单价 × 数量 + 教材费</span>
        </el-form-item>
        <el-form-item label="售价(元)" prop="sellingPrice">
          <el-input-number v-model="sellingPriceInYuan" :precision="2" :min="0" placeholder="请输入售价" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="上架">上架</el-radio>
            <el-radio label="下架">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" placeholder="请输入排序权重" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
