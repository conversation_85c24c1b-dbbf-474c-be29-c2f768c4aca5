# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: *************************************************************************************************************************************************************************************************************
                username: main
                password: 'Tongbu121!'
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 100
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT version()
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    data:
        # redis 配置
        redis:
            # 地址
            host: ************
            # 端口，默认为6379
            port: 9736
            # 数据库索引
            database: 0
            # 密码
            password: 'Redis123!'
            # 连接超时时间
            timeout: 10s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
    ai:
        openai:
            # 聊天模型
            chat:
                options:
                    model: Qwen/Qwen3-8B
            api-key: sk-tnesojkqpqozrtzfcxfkvucksdfgzmgysehqmrwbaoblwedi
            base-url: https://api.siliconflow.cn/
            temperature: ${OPENAI_TEMPERATURE:0.7}
            top-p: ${OPENAI_TOP_P:1.0}
            max-tokens: ${OPENAI_MAX_TOKENS:2000}

# 阿里云OSS配置
aliyun:
    oss:
        endpoint: oss-cn-shenzhen.aliyuncs.com
        accessKeyId: LTAI5tB4PDg4LaNBgbNVpW9g
        accessKeySecret: ******************************
        bucketName: tongbu-words
        urlPrefix: https://tongbu-words.oss-cn-shenzhen.aliyuncs.com/

nebius:
    base-url: https://api.studio.nebius.com
    api-key: eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMDI3NjM1MTUyODMwNDIwMDc1MyIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0MDg4MiwidXVpZCI6ImRjNzJlOGFmLTdkNDctNGUxNy04MjMyLTAwYmU1Yzc4Mjc4YyIsIm5hbWUiOiJ3b3JkcyIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEwOjQxOjIyKzAwMDAifQ.T3OWY8SA_6G2DNzHaD4E2iuioBi7s56pZX7GyGQwusk
    model: Qwen/Qwen3-32B
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890
xai:
    base-url: https://api.x.ai
    api-key: ************************************************************************************
    model: grok-3-mini
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890

word:
    audio:
        save-path: F:\\voice
        base-url: https://dict.youdao.com/dictvoice
    enrich:
        job:
            enabled: true
        words-model: siliconflow   # grok / siliconflow / nebuis，单词文本补全，默认为grok

volcengine:
    base-url: https://openspeech.bytedance.com
    speech:
        appid: 7167433942
        cluster: volcano_tts
        secretkey: Rgom2zgcp1VeySqr7oAefF8x4Et-MQTe
        access-token: lMA59dpe1S4RfAS2hJrm3Xt_xZR7L00_
        voice-type:
            us: zh_female_shuangkuaisisi_moon_bigtts
            uk: en_male_smith_mars_bigtts

clawcloud:
    url: https://iqzphfbrhgsd.ap-northeast-1.clawcloudrun.com/tts
    voice:
        us: en-US-AvaMultilingualNeural
        uk: en-GB-RyanNeural

wx:
    mp:
        job:
            enabled: true
        configs:
            - appId: wxcbd346eb954594be
              secret: fe9768a6cae65682409a29826e411638
              token: tongbu121
              aesKey: ThCvcb6slcFC4emnfJ3zU8pjtHQJlwFLiovxpihLBCO
              learningReportTemplateId: ZEzpJW0dBVYiC79yq5g92hc_r_PBMXv-G9kLIItncCY
              startCourseTemplateId: fO5rrvzp8Odp0JN-ApPqmx8kxef-qkrO3RSO5rgC5m8
        redirectUri: http://word.121tongbu.com/h5/#/pages/bind/index
        learningReportDetailUrl: http://word.121tongbu.com/h5/#/pages/course/report?id=%s
        reviewReportDetailUrl: http://word.121tongbu.com/h5/#/pages/course/review-report?id=%s

course:
    remind:
        job:
            enabled: true