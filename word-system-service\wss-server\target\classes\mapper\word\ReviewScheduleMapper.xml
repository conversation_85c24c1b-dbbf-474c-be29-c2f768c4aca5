<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.nonamespace.word.server.mapper.ReviewScheduleMapper">

    <!-- 基础查询字段 -->
    <sql id="selectReviewScheduleVo">
        SELECT
            rs.id,
            rs.student_id,
            u.nick_name as student_name,
            rs.course_id,
            rs.review_type,
            rs.name,
            rs.scheduled_time,
            rs.actual_start_time,
            rs.actual_end_time,
            rs.status,
            rs.word_ids,
            rs.textbook_item_ids,
            rs.version,
            rs.stat_word_total,
            rs.stat_word_correct,
            rs.stat_word_incorrect,
            rs.stat_step_total,
            rs.stat_step_correct,
            rs.stat_step_incorrect,
            rs.review_course_id,
            rs.review_by_oneself,
            rs.review_by_user_id,
            rs.uploaded_images,
            rs.uploaded_description,
            rs.uploaded_time,
            rs.create_time,
            rs.update_time
        FROM review_schedule rs
        LEFT JOIN sys_user u ON rs.student_id = u.user_id::varchar
    </sql>

    <!-- 查询条件 -->
    <sql id="whereCondition">
        <where>
            rs.deleted = false
            <if test="req.keyword != null and req.keyword != ''">
                AND rs.name LIKE '%'||#{req.keyword}||'%'
            </if>
            <if test="req.teacherId != null and req.teacherId != ''">
                AND (
                    EXISTS (
                    SELECT 1 FROM teacher_student_relation tsr
                    WHERE tsr.student_id = rs.student_id
                    AND tsr.teacher_id = #{req.teacherId}
                    AND tsr.deleted = false
                    AND tsr.status = 'active'
                    )
                    OR rs.review_by_user_id = #{req.teacherId}
                )
            </if>
            <if test="req.studentId != null and req.studentId != ''">
                AND rs.student_id = #{req.studentId}
            </if>
            <if test="req.reviewTypes != null and req.reviewTypes.size() > 0">
                AND rs.review_type IN
                <foreach collection="req.reviewTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="req.statuses != null and req.statuses.size() > 0">
                AND rs.status IN
                <foreach collection="req.statuses" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="req.startTime != null">
                AND rs.scheduled_time >= #{req.startTime}
            </if>
            <if test="req.endTime != null">
                AND rs.scheduled_time &lt;= #{req.endTime}
            </if>
        </where>
    </sql>

    <!-- 排序条件 -->
    <sql id="orderByCondition">
        ORDER BY
        <choose>
            <when test="req.orderBy == 'scheduled_time'">
                rs.scheduled_time
            </when>
            <when test="req.orderBy == 'create_time'">
                rs.create_time
            </when>
            <when test="req.orderBy == 'status'">
                rs.status
            </when>
            <when test="req.orderBy == 'review_type'">
                rs.review_type
            </when>
            <otherwise>
                rs.scheduled_time
            </otherwise>
        </choose>
        <choose>
            <when test="req.orderDirection == 'DESC'">
                DESC
            </when>
            <otherwise>
                ASC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询抗遗忘复习计划（关联学生信息） -->
    <select id="selectPageWithStudent" resultType="org.nonamespace.word.server.dto.ReviewScheduleQueryDto$Resp">
        <include refid="selectReviewScheduleVo"/>
        <include refid="whereCondition"/>
        <include refid="orderByCondition"/>
    </select>

    <!-- 查询抗遗忘复习计划列表（关联学生信息） -->
    <select id="selectListWithStudent" resultType="org.nonamespace.word.server.dto.ReviewScheduleQueryDto$Resp">
        <include refid="selectReviewScheduleVo"/>
        <include refid="whereCondition"/>
        <include refid="orderByCondition"/>
    </select>

</mapper>